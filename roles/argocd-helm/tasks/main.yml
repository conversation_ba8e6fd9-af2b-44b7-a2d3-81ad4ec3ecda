---
- name: Get currently installed helm applications
  kubernetes.core.helm_info:
    name: argocd
    release_namespace: "{{ argocd_namespace }}"
    kubeconfig: "{{ argocd_kubeconfig_location }}"
  register: argocd_info

# We need do this because there is some conflicts where the previous argocd installation
# was not created with helm and is missing the app.kubernetes.io/managed-by: Helm label.
# So we need to remove the old resources before we can install the new helm chart
- name: If the argocd helm app is not installed, purge the old resources
  include_tasks: purge.yml
  when: argocd_info.status is not defined

# TODO: Remove this when the eyecue app no longer creates resources in the infra namespace
- name: Create infra namespace # the eyecue namespace will be created by argocd
  kubernetes.core.k8s:
    name: infra
    api_version: v1
    kind: Namespace
    kubeconfig: "{{ argocd_kubeconfig_location }}"

- name: Setup ArgoCD
  include_tasks: setup.yml

# when the argocd app is not installed, we need to sync it
- name: Sync ArgoCD
  include_tasks: sync.yml
  when: argocd_info.status is not defined
