---
# https://gist.github.com/ebuildy/********************************
- name: Login and get token
  ansible.builtin.uri:
    url: "{{ argocd_url }}/api/v1/session"
    method: POST
    validate_certs: false
    body_format: json
    body:
      username: admin
      password: "{{ argocd_admin_password_raw }}"
    return_content: true
    status_code: 200
    headers:
      Content-Type: application/json
  register: login
  no_log: true

# First we need to sync the argocd-apps application
- name: Sync ArgoCD application argocd-apps
  register: async_result
  async: 45
  poll: 0
  ansible.builtin.uri:
    url: "{{ argocd_url }}/api/v1/applications/argocd-apps/sync"
    method: POST
    headers:
      Authorization: Bearer {{ login.json.token }}

- name: Wait for ArgoCD application sync
  ansible.builtin.async_status:
    jid: "{{ async_result.ansible_job_id }}"
  register: async_poll_results
  until: async_poll_results.finished
  retries: 30

- name: Wait to allow ArgoCD to register new applications
  ansible.builtin.wait_for:
    timeout: 5

# Then we can sync all the other applications
- name: Get all ArgoCD Applications
  ansible.builtin.uri:
    url: "{{ argocd_url }}/api/v1/applications"
    method: GET
    headers:
      Authorization: Bearer {{ login.json.token }}
  register: argocd_apps

- name: Sync ArgoCD applications
  register: async_results
  async: 45
  poll: 0
  ansible.builtin.uri:
    url: "{{ argocd_url }}/api/v1/applications/{{ application_name }}/sync"
    method: POST
    headers:
      Authorization: Bearer {{ login.json.token }}
  loop: "{{ argocd_apps.json['items'] | json_query('[*].metadata.name') }}"
  loop_control:
    loop_var: "application_name"

- name: Wait for ArgoCD application sync
  ansible.builtin.async_status:
    jid: "{{ async_result_item.ansible_job_id }}"
  loop: "{{ async_results.results }}"
  loop_control:
    loop_var: "async_result_item"
  register: async_poll_results
  until: async_poll_results.finished
  retries: 30
