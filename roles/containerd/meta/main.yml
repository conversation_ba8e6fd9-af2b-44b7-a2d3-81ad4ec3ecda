---
dependencies: []

galaxy_info:
  role_name: containerd
  author: geerlingguy
  description: containerd.io for Linux.
  company: "Midwestern Mac, LLC"
  license: "license (BSD, MIT)"
  min_ansible_version: 2.10
  platforms:
    - name: Fedora
      versions:
        - all
    - name: Debian
      versions:
        - stretch
        - buster
    - name: Ubuntu
      versions:
        - xenial
        - bionic
        - focal
  galaxy_tags:
    - system
    - containers
    - docker
    - containerd
    - kubernetes
    - orchestration
    - server
