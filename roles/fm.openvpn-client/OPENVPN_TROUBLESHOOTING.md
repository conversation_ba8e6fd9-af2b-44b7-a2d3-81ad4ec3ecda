# OpenVPN Client Troubleshooting Guide

This guide helps troubleshoot common issues with the OpenVPN client setup for the enhanced whitelist VPN gateway.

## Quick Diagnosis

Run the automated troubleshooting script:
```bash
sudo /usr/local/bin/vpn-troubleshoot.sh
```

## Common Issues and Solutions

### 1. Service Fails to Start (Exit Code 1)

**Symptoms:**
- `systemctl status openvpn-client` shows "failed" with exit code 1
- VPN manager shows "VPN connection failed after 30 seconds"

**Diagnosis:**
```bash
# Check service status
sudo systemctl status openvpn-client

# Check logs
sudo journalctl -u openvpn-client -f

# Check OpenVPN logs
sudo tail -f /var/log/openvpn/service.log
```

**Solutions:**
1. **Network not ready**: The service may be starting before network is fully configured
   ```bash
   # Wait for network and restart
   sudo systemctl restart systemd-networkd
   sudo systemctl restart openvpn-client
   ```

2. **DNS resolution issues**: VPN server hostname cannot be resolved
   ```bash
   # Test DNS resolution
   dig eyecue-whitelist-vpn-francium-lb-f943a130c03e187d.elb.ap-southeast-2.amazonaws.com
   
   # If DNS fails, check /etc/resolv.conf
   cat /etc/resolv.conf
   ```

3. **Routing issues**: VPN server route not properly configured
   ```bash
   # Check current routes
   ip route show
   
   # Manually add VPN server route
   sudo /usr/local/bin/vpn-pre-start.sh
   ```

### 2. Manual Connection Works, Service Doesn't

**Symptoms:**
- `openvpn --config /etc/openvpn/client/client.ovpn --verb 4` works
- `systemctl start openvpn-client` fails

**Root Cause:**
The systemd service has different environment and security restrictions.

**Solutions:**
1. **Check systemd restrictions**: The service runs with security hardening
   ```bash
   # Test with relaxed security (temporary)
   sudo systemctl edit openvpn-client
   # Add:
   [Service]
   PrivateDevices=false
   ProtectSystem=false
   ```

2. **Check file permissions**:
   ```bash
   ls -la /etc/openvpn/client/client.ovpn
   ls -la /dev/net/tun
   ```

### 3. Connection Timeout Issues

**Symptoms:**
- Connection attempts timeout after 30 seconds
- "connect-timeout" errors in logs

**Solutions:**
1. **Check VPN server connectivity**:
   ```bash
   # Test UDP connectivity
   nc -u -z eyecue-whitelist-vpn-francium-lb-f943a130c03e187d.elb.ap-southeast-2.amazonaws.com 1194
   
   # Test with different timeout
   timeout 10 ping eyecue-whitelist-vpn-francium-lb-f943a130c03e187d.elb.ap-southeast-2.amazonaws.com
   ```

2. **Firewall issues**:
   ```bash
   # Check UFW status
   sudo ufw status
   
   # Allow OpenVPN traffic
   sudo ufw allow out 1194/udp
   ```

### 4. TUN Interface Issues

**Symptoms:**
- "Cannot allocate TUN/TAP dev" errors
- No tun0 interface after connection

**Solutions:**
1. **Load TUN module**:
   ```bash
   sudo modprobe tun
   lsmod | grep tun
   ```

2. **Create TUN device**:
   ```bash
   sudo mkdir -p /dev/net
   sudo mknod /dev/net/tun c 10 200
   sudo chmod 600 /dev/net/tun
   ```

## Manual Testing Steps

### 1. Test Network Connectivity
```bash
# Check interface status
ip addr show eno2

# Check default route
ip route show | grep default

# Test internet connectivity
curl -s ifconfig.me
```

### 2. Test VPN Server Connectivity
```bash
# DNS resolution
dig +short eyecue-whitelist-vpn-francium-lb-f943a130c03e187d.elb.ap-southeast-2.amazonaws.com

# Ping test
ping -c 3 eyecue-whitelist-vpn-francium-lb-f943a130c03e187d.elb.ap-southeast-2.amazonaws.com

# Port test
nc -u -z eyecue-whitelist-vpn-francium-lb-f943a130c03e187d.elb.ap-southeast-2.amazonaws.com 1194
```

### 3. Test OpenVPN Configuration
```bash
# Validate config syntax
sudo openvpn --config /etc/openvpn/client/client.ovpn --verb 1 --test-crypto

# Test connection with verbose output
sudo openvpn --config /etc/openvpn/client/client.ovpn --verb 4
```

## Log Files

- **Service logs**: `/var/log/openvpn/service.log`
- **Client logs**: `/var/log/openvpn/client.log`
- **Pre-start logs**: `/var/log/openvpn/pre-start.log`
- **Post-stop logs**: `/var/log/openvpn/post-stop.log`
- **Systemd journal**: `journalctl -u openvpn-client`

## Recovery Steps

### Complete Reset
```bash
# Stop service
sudo systemctl stop openvpn-client

# Clean up processes
sudo pkill -f openvpn

# Remove routes
sudo ip route flush table main

# Restart networking
sudo systemctl restart systemd-networkd

# Restart service
sudo systemctl start openvpn-client
```

### Service Restart with Debugging
```bash
# Enable debug logging
sudo systemctl edit openvpn-client
# Add:
[Service]
ExecStart=
ExecStart=/usr/sbin/openvpn --suppress-timestamps --nobind --config /etc/openvpn/client/client.ovpn --verb 6 --log /var/log/openvpn/debug.log

# Restart and monitor
sudo systemctl daemon-reload
sudo systemctl restart openvpn-client
sudo tail -f /var/log/openvpn/debug.log
```

## Getting Help

If issues persist:

1. Run the troubleshooting script and save output:
   ```bash
   sudo /usr/local/bin/vpn-troubleshoot.sh > vpn-troubleshoot-report.txt
   ```

2. Collect relevant logs:
   ```bash
   sudo journalctl -u openvpn-client --since "1 hour ago" > systemd-logs.txt
   sudo tail -100 /var/log/openvpn/service.log > service-logs.txt
   ```

3. Test manual connection and capture output:
   ```bash
   sudo openvpn --config /etc/openvpn/client/client.ovpn --verb 4 > manual-test.txt 2>&1
   ```

Provide these files when requesting support.
