# OpenVPN Client Role

This role installs and configures OpenVPN client on edge servers to connect to the enhanced whitelist VPN gateway.

## Features

- Installs OpenVPN client and configures certificates
- Creates VPN management scripts
- Sets up systemd service for VPN management
- Configures eno2 interface for VPN traffic (edge server standard)

## Required Files

- `files/client.ovpn`: Complete OpenVPN client configuration with certificates

## Key Variables

- `openvpn_server_hostname`: VPN server hostname/FQDN
- `openvpn_client_auto_start`: Auto-start VPN on boot (default: false)
- `openvpn_client_wired_interface`: Primary interface (default: eno2)

## Usage

The role is designed to be used with the `infra_openvpn_client.yml` playbook which contains all necessary configuration.

## Management Commands

After installation:

- `sudo /usr/local/bin/vpn-manager.sh start` - Start VPN
- `sudo /usr/local/bin/vpn-manager.sh status` - Check status
- `sudo /usr/local/bin/vpn-manager.sh stop` - Stop VPN
- `sudo systemctl enable openvpn-client` - Enable auto-start
