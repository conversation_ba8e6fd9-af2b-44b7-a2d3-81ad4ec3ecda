---
# OpenVPN Client Role Default Variables

# VPN Configuration
openvpn_client_auto_start: false
openvpn_client_log_level: 3

# Network Interface Configuration
# Edge servers use eno2 as primary interface (based on standard setup)
openvpn_client_wired_interface: "eno2"

# Directory Configuration
openvpn_client_config_dir: "/etc/openvpn/client"
openvpn_client_log_dir: "/var/log/openvpn"
openvpn_client_script_dir: "/usr/local/bin"

# Service Configuration
openvpn_client_service_name: "openvpn-client"
openvpn_client_config_file: "client.ovpn"

# Connection Settings
openvpn_client_connect_timeout: 30
openvpn_client_ping_interval: 10
openvpn_client_ping_restart: 120

# Required Variables (must be set by user)
# openvpn_server_hostname: ""       # VPN server hostname/FQDN

# Note: The client.ovpn file should be placed in the role's files/ directory
