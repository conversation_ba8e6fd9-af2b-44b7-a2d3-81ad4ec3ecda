---
# Create VPN Management Scripts

- name: Create VPN management script
  template:
    src: vpn-manager.sh.j2
    dest: "{{ openvpn_client_script_dir }}/vpn-manager.sh"
    owner: root
    group: root
    mode: '0755'
    backup: true

- name: Create VPN connection check script
  template:
    src: vpn-check.sh.j2
    dest: "{{ openvpn_client_script_dir }}/vpn-check.sh"
    owner: root
    group: root
    mode: '0755'

- name: Create VPN status monitoring script
  template:
    src: vpn-monitor.sh.j2
    dest: "{{ openvpn_client_script_dir }}/vpn-monitor.sh"
    owner: root
    group: root
    mode: '0755'

- name: Create convenient symlinks for VPN management
  file:
    src: "{{ openvpn_client_script_dir }}/vpn-manager.sh"
    dest: "/usr/local/bin/{{ item }}"
    state: link
    force: true
  loop:
    - vpn-connect
    - vpn-disconnect
    - vpn-status
