[Unit]
Description=OpenVPN Client for Enhanced Whitelist Gateway
After=network-online.target systemd-resolved.service
Wants=network-online.target
Requires=network-online.target
Documentation=man:openvpn(8)

[Service]
Type=notify
PrivateTmp=true
WorkingDirectory={{ openvpn_client_config_dir }}
ExecStartPre=/bin/bash -c 'until ping -c1 {{ openvpn_server_hostname }} >/dev/null 2>&1; do sleep 2; done'
ExecStartPre=/usr/local/bin/vpn-pre-start.sh
ExecStart=/usr/sbin/openvpn --suppress-timestamps --nobind --config {{ openvpn_client_config_dir }}/{{ openvpn_client_config_file }} --verb 4 --log {{ openvpn_client_log_dir }}/service.log
ExecReload=/bin/kill -HUP $MAINPID
ExecStopPost=/usr/local/bin/vpn-post-stop.sh
CapabilityBoundingSet=CAP_IPC_LOCK CAP_NET_ADMIN CAP_NET_RAW CAP_SETGID CAP_SETUID CAP_SYS_CHROOT CAP_DAC_OVERRIDE
LimitNPROC=10
DeviceAllow=/dev/null rw
DeviceAllow=/dev/net/tun rw
ProtectSystem=true
ProtectHome=true
KillMode=process
RestartSec=10s
Restart=on-failure
TimeoutStartSec=120s

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=openvpn-client

# Security settings
NoNewPrivileges=true
PrivateDevices=true
ProtectControlGroups=true
ProtectKernelModules=true
ProtectKernelTunables=true
RestrictAddressFamilies=AF_UNIX AF_INET AF_INET6 AF_NETLINK
RestrictNamespaces=true
RestrictRealtime=true
SystemCallArchitectures=native

[Install]
WantedBy=multi-user.target
