#!/bin/bash
# OpenVPN Troubleshooting Script
# Generated by Ansible - DO NOT EDIT MANUALLY

# Configuration
VPN_CONFIG="{{ openvpn_client_config_dir }}/{{ openvpn_client_config_file }}"
WIRED_INTERFACE="{{ openvpn_client_wired_interface }}"
SERVICE_NAME="{{ openvpn_client_service_name }}"
LOG_FILE="{{ openvpn_client_log_dir }}/client.log"
SERVICE_LOG_FILE="{{ openvpn_client_log_dir }}/service.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== OpenVPN Troubleshooting Report ===${NC}"
echo "Generated: $(date)"
echo ""

# System Information
echo -e "${YELLOW}--- System Information ---${NC}"
echo "OS: $(lsb_release -d 2>/dev/null | cut -f2 || cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "Kernel: $(uname -r)"
echo "OpenVPN Version: $(openvpn --version | head -1)"
echo ""

# Network Configuration
echo -e "${YELLOW}--- Network Configuration ---${NC}"
echo "Primary Interface ($WIRED_INTERFACE):"
ip addr show "$WIRED_INTERFACE" 2>/dev/null || echo "  Interface not found"
echo ""
echo "Default Routes:"
ip route show | grep default
echo ""
echo "DNS Configuration:"
cat /etc/resolv.conf | grep nameserver
echo ""

# VPN Server Connectivity
echo -e "${YELLOW}--- VPN Server Connectivity ---${NC}"
VPN_SERVER=$(grep "^remote " "$VPN_CONFIG" | awk '{print $2}' | head -1)
VPN_PORT=$(grep "^remote " "$VPN_CONFIG" | awk '{print $3}' | head -1)
echo "VPN Server: $VPN_SERVER:$VPN_PORT"

if [ -n "$VPN_SERVER" ]; then
    echo "DNS Resolution:"
    dig +short "$VPN_SERVER" | head -3
    echo ""
    
    echo "Ping Test:"
    ping -c 3 "$VPN_SERVER" 2>/dev/null || echo "  Ping failed"
    echo ""
    
    echo "Port Connectivity:"
    timeout 5 nc -u -z "$VPN_SERVER" "$VPN_PORT" 2>/dev/null && echo "  Port $VPN_PORT/UDP: Open" || echo "  Port $VPN_PORT/UDP: Closed or filtered"
fi
echo ""

# Service Status
echo -e "${YELLOW}--- Service Status ---${NC}"
echo "Service Status:"
systemctl status "$SERVICE_NAME" --no-pager -l
echo ""

# TUN Interface
echo -e "${YELLOW}--- TUN Interface ---${NC}"
if ip addr show tun0 >/dev/null 2>&1; then
    echo -e "${GREEN}TUN interface exists:${NC}"
    ip addr show tun0
else
    echo -e "${RED}TUN interface not found${NC}"
fi
echo ""

# Log Analysis
echo -e "${YELLOW}--- Recent Log Entries ---${NC}"
echo "Client Log (last 20 lines):"
if [ -f "$LOG_FILE" ]; then
    tail -20 "$LOG_FILE"
else
    echo "  Log file not found: $LOG_FILE"
fi
echo ""

echo "Service Log (last 20 lines):"
if [ -f "$SERVICE_LOG_FILE" ]; then
    tail -20 "$SERVICE_LOG_FILE"
else
    echo "  Service log file not found: $SERVICE_LOG_FILE"
fi
echo ""

echo "Systemd Journal (last 20 entries):"
journalctl -u "$SERVICE_NAME" -n 20 --no-pager
echo ""

# Configuration Check
echo -e "${YELLOW}--- Configuration Check ---${NC}"
echo "OpenVPN Config File:"
if [ -f "$VPN_CONFIG" ]; then
    echo "  File exists: $VPN_CONFIG"
    echo "  File size: $(stat -c%s "$VPN_CONFIG") bytes"
    echo "  Permissions: $(stat -c%A "$VPN_CONFIG")"
    echo ""
    echo "  Key configuration lines:"
    grep -E "^(client|dev|proto|remote|cipher|auth)" "$VPN_CONFIG" | head -10
else
    echo -e "  ${RED}Config file not found: $VPN_CONFIG${NC}"
fi
echo ""

# Process Information
echo -e "${YELLOW}--- Process Information ---${NC}"
echo "OpenVPN Processes:"
ps aux | grep openvpn | grep -v grep || echo "  No OpenVPN processes found"
echo ""

# Firewall Status
echo -e "${YELLOW}--- Firewall Status ---${NC}"
if command -v ufw >/dev/null 2>&1; then
    echo "UFW Status:"
    ufw status
elif command -v iptables >/dev/null 2>&1; then
    echo "IPTables Rules (INPUT chain):"
    iptables -L INPUT -n | head -10
fi
echo ""

# Recommendations
echo -e "${YELLOW}--- Troubleshooting Recommendations ---${NC}"
echo "1. Check if VPN server is reachable: ping $VPN_SERVER"
echo "2. Verify port connectivity: nc -u -z $VPN_SERVER $VPN_PORT"
echo "3. Test manual connection: openvpn --config $VPN_CONFIG --verb 4"
echo "4. Check service logs: journalctl -u $SERVICE_NAME -f"
echo "5. Restart service: systemctl restart $SERVICE_NAME"
echo ""

echo -e "${BLUE}=== End of Troubleshooting Report ===${NC}"
