#!/bin/bash
# OpenVPN Pre-Start Script
# Generated by Ansible - DO NOT EDIT MANUALLY

set -euo pipefail

# Configuration
WIRED_INTERFACE="{{ openvpn_client_wired_interface }}"
VPN_CONFIG="{{ openvpn_client_config_dir }}/{{ openvpn_client_config_file }}"
LOG_FILE="{{ openvpn_client_log_dir }}/pre-start.log"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log "Starting OpenVPN pre-start setup"

# Ensure wired interface is up and configured
log "Ensuring wired interface $WIRED_INTERFACE is up and configured"
ip link set "$WIRED_INTERFACE" up 2>/dev/null || true

# Wait for interface to get IP if using DHCP
if ! ip addr show "$WIRED_INTERFACE" | grep -q 'inet '; then
    log "Waiting for $WIRED_INTERFACE to get IP address"
    for i in {1..30}; do
        if ip addr show "$WIRED_INTERFACE" | grep -q 'inet '; then
            break
        fi
        sleep 1
    done
fi

# Get current network configuration
WIRED_IP=$(ip addr show "$WIRED_INTERFACE" | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1 | head -1)
GATEWAY=$(ip route show | grep "^default" | grep "$WIRED_INTERFACE" | awk '{print $3}' | head -1)

log "Network configuration: Interface=$WIRED_INTERFACE, IP=$WIRED_IP, Gateway=$GATEWAY"

# Add route for VPN server to ensure it goes through the wired interface
VPN_SERVER=$(grep "^remote " "$VPN_CONFIG" | awk '{print $2}' | head -1)
if [ -n "$VPN_SERVER" ] && [ -n "$GATEWAY" ]; then
    VPN_SERVER_IP=$(dig +short "$VPN_SERVER" | head -1)
    if [ -n "$VPN_SERVER_IP" ]; then
        log "Adding route for VPN server $VPN_SERVER ($VPN_SERVER_IP) via $GATEWAY"
        ip route add "$VPN_SERVER_IP/32" via "$GATEWAY" dev "$WIRED_INTERFACE" 2>/dev/null || true
    fi
fi

# Ensure TUN module is loaded
log "Loading TUN module"
modprobe tun 2>/dev/null || true

# Create TUN device if it doesn't exist
if [ ! -c /dev/net/tun ]; then
    log "Creating TUN device"
    mkdir -p /dev/net
    mknod /dev/net/tun c 10 200
    chmod 600 /dev/net/tun
fi

log "Pre-start setup completed successfully"
