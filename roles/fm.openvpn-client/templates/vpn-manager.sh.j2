#!/bin/bash
# OpenVPN Client Management Script
# Generated by Ansible - DO NOT EDIT MANUALLY

# Configuration
VPN_CONFIG="{{ openvpn_client_config_dir }}/{{ openvpn_client_config_file }}"
WIRED_INTERFACE="{{ openvpn_client_wired_interface }}"
WIRED_IP="{{ openvpn_client_wired_ip | default('') }}"
GATEWAY="{{ openvpn_client_gateway | default('') }}"
LOG_FILE="{{ openvpn_client_log_dir }}/client.log"
PID_FILE="/var/run/openvpn-client.pid"
SERVICE_NAME="{{ openvpn_client_service_name }}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Check if VPN is running
check_vpn_status() {
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        return 0
    else
        return 1
    fi
}

# Check if tunnel interface exists
check_tunnel_interface() {
    if ip addr show tun0 >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Ensure wired interface is properly configured
ensure_wired_interface() {
    log "Ensuring wired interface $WIRED_INTERFACE is up and configured"
    ip link set "$WIRED_INTERFACE" up 2>/dev/null || true

    # Refresh DHCP lease if needed
    if [ -z "$WIRED_IP" ]; then
        log "Refreshing DHCP lease on $WIRED_INTERFACE"
        dhclient "$WIRED_INTERFACE" 2>/dev/null &
    fi
}

# Add route for VPN server
add_vpn_server_route() {
    VPN_SERVER=$(grep "^remote " "$VPN_CONFIG" | awk '{print $2}' | head -1)
    if [ -n "$VPN_SERVER" ]; then
        VPN_SERVER_IP=$(dig +short "$VPN_SERVER" | head -1)
        if [ -n "$VPN_SERVER_IP" ]; then
            # Try to use configured gateway first
            if [ -n "$GATEWAY" ]; then
                log "Adding route for VPN server $VPN_SERVER ($VPN_SERVER_IP) via $GATEWAY"
                ip route add "$VPN_SERVER_IP/32" via "$GATEWAY" dev "$WIRED_INTERFACE" 2>/dev/null || true
            else
                # Try to find gateway for the wired interface
                DETECTED_GATEWAY=$(ip route show | grep "^default" | grep "$WIRED_INTERFACE" | awk '{print $3}' | head -1)
                if [ -n "$DETECTED_GATEWAY" ]; then
                    log "Adding route for VPN server $VPN_SERVER ($VPN_SERVER_IP) via detected gateway $DETECTED_GATEWAY"
                    ip route add "$VPN_SERVER_IP/32" via "$DETECTED_GATEWAY" dev "$WIRED_INTERFACE" 2>/dev/null || true
                else
                    log "Warning: No gateway found for $WIRED_INTERFACE, skipping VPN server route"
                fi
            fi
        fi
    fi
}

# Start VPN connection
start_vpn() {
    echo -e "${YELLOW}Starting VPN connection...${NC}"
    
    if check_vpn_status; then
        echo -e "${GREEN}✓ VPN is already running${NC}"
        return 0
    fi
    
    log "Starting VPN connection"

    # Ensure wired interface is properly configured
    ensure_wired_interface

    # Add VPN server route
    add_vpn_server_route
    
    # Start the systemd service
    systemctl start "$SERVICE_NAME"
    
    # Wait for connection
    echo "Waiting for VPN connection to establish..."
    for i in {1..{{ openvpn_client_connect_timeout }}}; do
        if check_tunnel_interface; then
            sleep 2  # Give it a moment to fully establish
            VPN_IP=$(ip addr show tun0 | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1)
            PUBLIC_IP=$(curl -s --max-time 10 ifconfig.me 2>/dev/null || echo 'Unable to determine')
            
            echo -e "${GREEN}✓ VPN connected successfully!${NC}"
            echo "VPN Interface IP: $VPN_IP"
            echo "Public IP: $PUBLIC_IP"
            log "VPN connected - VPN IP: $VPN_IP, Public IP: $PUBLIC_IP"
            return 0
        fi
        echo "Waiting... ($i/{{ openvpn_client_connect_timeout }})"
        sleep 2
    done
    
    echo -e "${RED}✗ VPN connection failed${NC}"
    log "VPN connection failed after {{ openvpn_client_connect_timeout }} seconds"
    return 1
}

# Stop VPN connection
stop_vpn() {
    echo -e "${YELLOW}Stopping VPN connection...${NC}"
    
    log "Stopping VPN connection"
    
    # Stop the systemd service
    systemctl stop "$SERVICE_NAME" 2>/dev/null || true
    
    # Clean up any remaining processes
    pkill -f "openvpn.*{{ openvpn_client_config_file }}" 2>/dev/null || true

    # Refresh wired network configuration
    log "Refreshing network configuration on $WIRED_INTERFACE"
    dhclient "$WIRED_INTERFACE" 2>/dev/null &
    
    echo -e "${GREEN}✓ VPN disconnected${NC}"
    log "VPN disconnected and network restored"
}

# Show VPN status
show_status() {
    echo "=== OpenVPN Client Status ==="
    
    if check_vpn_status; then
        echo -e "Service Status: ${GREEN}Running${NC}"
        
        if check_tunnel_interface; then
            VPN_IP=$(ip addr show tun0 | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1)
            PUBLIC_IP=$(curl -s --max-time 10 ifconfig.me 2>/dev/null || echo 'Unable to determine')
            
            echo -e "Tunnel Status: ${GREEN}Connected${NC}"
            echo "VPN Interface IP: $VPN_IP"
            echo "Public IP: $PUBLIC_IP"
        else
            echo -e "Tunnel Status: ${RED}Disconnected${NC}"
        fi
    else
        echo -e "Service Status: ${RED}Stopped${NC}"
        echo -e "Tunnel Status: ${RED}Disconnected${NC}"
    fi
    
    echo ""
    echo "Network Interface:"
    echo "  Primary ($WIRED_INTERFACE): $(ip addr show "$WIRED_INTERFACE" 2>/dev/null | grep 'inet ' | awk '{print $2}' || echo 'Not configured')"
    
    echo ""
    echo "Recent log entries:"
    tail -n 5 "$LOG_FILE" 2>/dev/null || echo "No log entries found"
}

# Show logs
show_logs() {
    echo "=== OpenVPN Client Logs ==="
    if [ -f "$LOG_FILE" ]; then
        tail -f "$LOG_FILE"
    else
        echo "No log file found at $LOG_FILE"
    fi
}

# Main script logic
case "${1:-status}" in
    "start")
        start_vpn
        ;;
    "stop")
        stop_vpn
        ;;
    "restart")
        stop_vpn
        sleep 2
        start_vpn
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs}"
        echo "  start   - Connect to VPN"
        echo "  stop    - Disconnect VPN and restore network"
        echo "  restart - Restart VPN connection"
        echo "  status  - Show detailed VPN status"
        echo "  logs    - View VPN logs (follow mode)"
        exit 1
        ;;
esac
