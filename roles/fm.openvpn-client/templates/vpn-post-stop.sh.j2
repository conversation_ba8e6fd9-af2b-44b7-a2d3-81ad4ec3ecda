#!/bin/bash
# OpenVPN Post-Stop Script
# Generated by Ansible - DO NOT EDIT MANUALLY

set -euo pipefail

# Configuration
WIRED_INTERFACE="{{ openvpn_client_wired_interface }}"
VPN_CONFIG="{{ openvpn_client_config_dir }}/{{ openvpn_client_config_file }}"
LOG_FILE="{{ openvpn_client_log_dir }}/post-stop.log"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log "Starting OpenVPN post-stop cleanup"

# Clean up any remaining OpenVPN processes
log "Cleaning up any remaining OpenVPN processes"
pkill -f "openvpn.*{{ openvpn_client_config_file }}" 2>/dev/null || true

# Remove VPN server route if it exists
VPN_SERVER=$(grep "^remote " "$VPN_CONFIG" | awk '{print $2}' | head -1)
if [ -n "$VPN_SERVER" ]; then
    VPN_SERVER_IP=$(dig +short "$VPN_SERVER" | head -1 2>/dev/null || true)
    if [ -n "$VPN_SERVER_IP" ]; then
        log "Removing route for VPN server $VPN_SERVER ($VPN_SERVER_IP)"
        ip route del "$VPN_SERVER_IP/32" 2>/dev/null || true
    fi
fi

# Refresh network configuration on wired interface
log "Refreshing network configuration on $WIRED_INTERFACE"
dhclient "$WIRED_INTERFACE" 2>/dev/null &

log "Post-stop cleanup completed"
