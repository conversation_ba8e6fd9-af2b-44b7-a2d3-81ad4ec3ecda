---
- name: Define Jumpcloud dependencies according to the platform
  set_fact:
    jumpcloud_dependencies: "{{ jumpcloud_dependencies + jumpcloud_all_dependencies }}"

- name: Define YUM based systems dependencies
  set_fact:
    jumpcloud_dependencies: "{{ jumpcloud_dependencies + jumpcloud_yum_dependencies }}"
  when: ansible_pkg_mgr == "yum"

- name: Define APT based systems dependencies
  set_fact:
    jumpcloud_dependencies: "{{ jumpcloud_dependencies + jumpcloud_apt_dependencies }}"
  when: ansible_pkg_mgr == "apt"

- name: Define Debian systems dependencies
  set_fact:
    jumpcloud_dependencies: "{{ jumpcloud_dependencies + jumpcloud_debian_dependencies }}"
  when: ansible_distribution == "Debian"

- name: Define Ubuntu systems dependencies
  set_fact:
    jumpcloud_dependencies: "{{ jumpcloud_dependencies + jumpcloud_ubuntu_dependencies }}"
  when: ansible_distribution == "Ubuntu"

- name: Install Jumpcloud dependencies
  package:
    name: "{{ jumpcloud_dependencies }}"
    state: present
  become: true
