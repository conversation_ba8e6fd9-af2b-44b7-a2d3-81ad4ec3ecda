---
- name: Add device to Infrastructure Hosts JumpCloud device group
  uri:
    url: "{{ jc_url }}"
    method: POST
    headers:
      "Content-Type": "application/json"
      "x-api-key": "{{ jumpcloud_api_key }}"
    body_format: json
    body: '{"op": "add", "type": "system", "id": "{{ jumpcloud_system_key }}" }'
    follow_redirects: all
    return_content: yes
    status_code: 204,409
  register: response
  when:
    - jc_system_is_registered
