---
- name: Check if JumpCloud is installed
  stat:
    path: "{{ jumpcloud_agent_config }}"
  register: jumpcloud_agent_config_status

- name: Set whether Jumpcloud is already installed
  set_fact:
    jumpcloud_is_installed: "{{ (jumpcloud_agent_config_status.stat.isreg is defined and jumpcloud_agent_config_status.stat.isreg) }}"

- name: Remove registered systems with the same displayName from JumpCloud
  import_tasks: remove_registered_systems.yml
  when: not jumpcloud_is_installed or jumpcloud_force_install

- name: Install JumpCloud dependencies if required
  import_tasks: install_deps.yml
  when: not jumpcloud_is_installed or jumpcloud_force_install

- name: Install JumpCloud if required
  import_tasks: install.yml
  when: not jumpcloud_is_installed or jumpcloud_force_install

- name: Get JumpCloud SystemKey
  import_tasks: get_system_key.yml
  when: jumpcloud_is_installed or jumpcloud_force_install

- name: Check System registration
  import_tasks: check_system_registration.yml
  when: jumpcloud_system_key is defined

- name: Add system to groups if groups are defined and the system is registered in JumpCloud
  import_tasks: update_groups.yml
  when: jc_system_is_registered
