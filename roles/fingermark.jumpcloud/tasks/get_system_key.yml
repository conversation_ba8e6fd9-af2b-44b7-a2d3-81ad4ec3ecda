---
- name: Install jq if not present
  ansible.builtin.apt:
    name: jq
    state: present

- name: Wait 15 Sec for SystemKey to be available
  ansible.builtin.wait_for:
    timeout: 15

- name: Retrieve JumpCloud device ID
  shell: cat /opt/jc/jcagent.conf | jq -r '.systemKey'
  register: jumpcloud_device_id
- set_fact: 
    jumpcloud_system_key: "{{ jumpcloud_device_id.stdout_lines[0] }}"

- name: Display JumpCloud device ID
  debug:
    var: jumpcloud_device_id.stdout_lines[0]
