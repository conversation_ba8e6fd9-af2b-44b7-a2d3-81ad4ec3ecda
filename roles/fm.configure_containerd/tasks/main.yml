# setup containerd

- name: Make sure dependencies are installed
  ansible.builtin.apt:
    name:
      - apt-transport-https
      - ca-certificates
      - curl
      - software-properties-common
    state: present
    update_cache: true
    cache_valid_time: "{{ microk8s_cache_valid_time }}"

- name: load modules
  ansible.builtin.shell: |
    cat <<EOF | sudo tee /etc/modules-load.d/containerd.conf overlay br_netfilter EOF
    sudo modprobe overlay && sudo modprobe br_netfilter

- name: setup required sysctl parameters
  ansible.builtin.shell: |
    cat <<EOF | sudo tee /etc/sysctl.d/99-kubernetes-cri.conf
    net.bridge.bridge-nf-call-iptables  = 1
    net.ipv4.ip_forward                 = 1
    net.bridge.bridge-nf-call-ip6tables = 1
    EOF
    sudo sysctl --system

- name: Add Docker PGP key
  apt_key:
    url: https://download.docker.com/linux/ubuntu/gpg
    state: present
    keyring: /etc/apt/trusted.gpg.d/docker.gpg

- name: Add docker apt repository
  apt_repository:
    repo: "deb [arch=amd64] https://download.docker.com/linux/ubuntu bionic stable"
    state: present

# - name: Add docker apt repository
#   ansible.builtin.shell: |
#       sudo add-apt-repository 

- name: install containerd service
  ansible.builtin.apt:
    name:
      - containerd.io
    state: present
    update_cache: true
    cache_valid_time: "{{ microk8s_cache_valid_time }}"
  register: containerd

- name: create config file for containerd
  ansible.builtin.shell: sudo mkdir -p /etc/containerd && sudo containerd config default | sudo tee /etc/containerd/config.toml

- name: Configure containerd to use the systemd cgroup driver with runc
  ansible.builtin.lineinfile:
    path: /etc/containerd/config.toml
    line: '            SystemdCgroup = true'
    regexp: 'SystemdCgroup = false'

- name: restart containerd service
  ansible.builtin.shell: sudo systemctl restart containerd
  when: containerd is defined and containerd.changed