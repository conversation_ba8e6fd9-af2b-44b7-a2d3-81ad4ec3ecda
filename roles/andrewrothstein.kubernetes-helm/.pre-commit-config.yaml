- repo: https://github.com/pre-commit/pre-commit-hooks
  sha: v0.8.0
  hooks:
    - id: trailing-whitespace
    - id: end-of-file-fixer
    - id: check-json
    - id: check-yaml
    - id: check-symlinks
    - id: detect-aws-credentials
      args:
        - --allow-missing-credentials
    - id: check-added-large-files
    - id: detect-private-key

- repo: https://github.com/willthames/ansible-lint.git
  sha: v3.4.13
  hooks:
    - id: ansible-lint
      files: \.(yaml|yml)$
      args: [
        "--exclude=roles",
        "--exclude=basic/roles",
        "--exclude=.travis.yml"
      ]
