- name: Check if storage mount point is already mounted
  ansible.builtin.command: findmnt -n {{ storage_mount_path }}
  register: mount_info
  changed_when: false
  failed_when: false

- name: Set flag to skip secondary mount tasks if storage is already mounted
  ansible.builtin.set_fact:
    skip_mount_tasks: "{{ mount_info.rc == 0 }}"

- name: Gather status of potential secondary storage devices
  ansible.builtin.stat:
    path: "{{ item.device }}"
  loop: "{{ storage_devices }}"
  register: device_stats
  when: not skip_mount_tasks

- name: Select first available secondary device from prioritized list
  ansible.builtin.set_fact:
    selected_device: >-
      {{
        (device_stats.results
          | selectattr('stat.exists', 'equalto', true)
          | map(attribute='item.device')
          | list)[0]
          | default(omit)
      }}
    selected_uuid_path: >-
      {{
        (device_stats.results
          | selectattr('stat.exists', 'equalto', true)
          | map(attribute='item.uuid_path')
          | list)[0]
          | default(omit)
      }}
  when: not skip_mount_tasks and
        (device_stats.results | selectattr('stat.exists', 'equalto', true) | list | length) > 0
  run_once: true

- name: Include mount volume tasks if a valid secondary device is found
  ansible.builtin.include_tasks: mount_volume.yml
  when: not skip_mount_tasks and selected_device is defined

- name: Include resize SSD tasks for primary disk on Ubuntu 22.04
  ansible.builtin.include_tasks: resize_ssd.yml
  when: ansible_distribution_version == "22.04"
