---
- name: Install eyecue-network-cli
  ansible.builtin.pip:
    name: git+https://x-token-auth:{{ network_cli_bb_access_token }}@bitbucket.org/fingermarkltd/{{ network_cli_repo }}@{{ network_cli_version }}
    virtualenv: "{{ network_cli_virtualenv }}"

- name: Soft link {{ network_cli_script_name }} to /usr/local/bin
  ansible.builtin.file:
    src: "{{ network_cli_virtualenv }}/bin/{{ network_cli_script_name}}"
    dest: "/usr/local/bin/{{ network_cli_script_name }}"
    state: link

- name: Add completion to bashrc
  ansible.builtin.lineinfile:
    path: /root/.bashrc
    line: "source <({{ network_cli_script_name }} --show-completion)"
