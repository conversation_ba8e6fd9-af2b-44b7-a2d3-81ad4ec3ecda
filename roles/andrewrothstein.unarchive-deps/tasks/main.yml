---
- name: resolve platform specific vars
  include_vars: "{{ item }}"
  with_first_found:
    - files:
        - "{{ ansible_distribution }}-{{ ansible_distribution_release }}.yml"
        - "{{ ansible_distribution }}.yml"
        - "{{ ansible_os_family }}.yml"
      skip: true
      paths:
        - '{{ role_path }}/vars'

- name: install common pkgs...
  become: '{{ unarchive_deps_privilege_escalation | default(True) }}'
  become_user: root
  package:
    name: '{{ unarchive_deps_all_pkgs + unarchive_deps_tar_pkgs + unarchive_deps_xz_pkgs }}'
    state: present
