---
# - name: Enabling WG proxy for USA clients
#   ansible.builtin.shell: /usr/local/bin/wg_client_connection.sh up
#   when: customer in ('cfa', 'czp')

- name: add apt signing key
  apt_key:
    keyserver: hkp://keyserver.ubuntu.com:80
    id: EE0CC692
    state: present

- name: add lacework repository into source list
  apt_repository:
    repo: "deb [arch=amd64] https://packages.lacework.net/latest/DEB/{{ ansible_distribution | lower  }} {{ ansible_distribution_release }} main"
    filename: lacework
    state: present
    update_cache: yes

- name: install lacework datacollector
  apt:
    name: lacework
    state: latest

- name: wait until /var/lib/lacework/config/ is created
  wait_for:
    path: /var/lib/lacework/config/

- name: Create Lacework agent config file
  ansible.builtin.copy:
    dest: "{{ config_path }}"
    content: |
      {
        "tokens": {
          "AccessToken": "{{ access_token }}"
        },
        "serverurl": "{{ server_url }}"
      }
    mode: '0600'

- name: Restart Lacework agent service
  ansible.builtin.systemd:
    name: datacollector.service
    state: restarted

- name: Verify Lacework agent service status
  ansible.builtin.systemd:
    name: datacollector.service
    state: started
    enabled: true

# - name: Shutdown WG proxy for USA clients
#   ansible.builtin.shell: /usr/local/bin/wg_client_connection.sh down
#   when: customer in ('cfa', 'czp')