#!/usr/bin/python3

import subprocess

try:
  process_clean = subprocess.run(['meshcmd', 'amtscript','--script', '/usr/local/bin/cira_clean.mescript', '--user', 'admin', '--pass', '{{ amt_password }}'], timeout=60)
  process_clean.communicate()
except:
  print('ERROR: timeout exceeded...')

try:
  process_setup = subprocess.run(['meshcmd', 'amtscript','--script', '/usr/local/bin/cira_setup.mescript', '--user', 'admin', '--pass', '{{ amt_password }}'], timeout=60)
  process_setup.communicate()
except:
  print('ERROR: timeout exceeded...')
