---

- name: Downloading meshcmd...
  get_url: 
    url: http://alt.meshcentral.com/meshagents?meshcmd=6
    dest: /usr/local/bin/meshcmd
    mode: 700
  
- name: "Downloading client's script 1/2"
  get_url: 
    url: "{{ fingermark_meshcentral_url }}?type=1&meshid={{ meshid }}"
    dest: /usr/local/bin/cira_setup.mescript
    validate_certs: no

- name: "Downloading client's script 2/2"
  get_url: 
    url: "{{ fingermark_meshcentral_url }}?type=2"
    dest: /usr/local/bin/cira_clean.mescript
    validate_certs: no

# Enable gathering facts for this: 
# - name: Getting AMT info
#   shell: meshcmd amtinfo    
#   register: amtinfo
#   ignore_errors: true

# - name: Generating file with AMT info
#   lineinfile:
#     line: '{{ ansible_hostname }}: {{ amtinfo.stdout }}'
#     dest: /home/<USER>/Downloads/amtinfo.log
#     insertafter: EOF
#   delegate_to: localhost
  
- name: Setup cronjob
  include_tasks: meshcmd_cronjob.yaml

- name: Run command for the first time
  command: /usr/local/bin/meshcmd_cron.py