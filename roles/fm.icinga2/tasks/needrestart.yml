---
- name: Installing needrestart package
  ansible.builtin.apt:
    name: needrestart
    state: present

# New implementation for maintenance window. Disabling it for now...
# - name: Creating cronjob file
#   ansible.builtin.template:
#     src: cronjob_needrestart
#     dest: /etc/cron.d/needrestart

- name: Creating cronjob file
  ansible.builtin.file:
    path: /etc/cron.d/needrestart
    state: touch

- name: Copying nagios plugin
  ansible.builtin.template:
    src: check_needrestart
    dest: /usr/lib/nagios/plugins/check_needrestart
    mode: "775"

- name: Copying Auto ACK script
  ansible.builtin.copy:
    src: icinga2_auto_ack
    dest: /usr/local/bin
    mode: "775"

