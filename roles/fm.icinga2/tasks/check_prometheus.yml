---
- name: "Installing dependencies"
  ansible.builtin.pip:
    name: requests==2.27.1

- name: Copying Kubernetes nagios plugin
  ansible.builtin.copy:
    src: check_prometheus
    dest: /usr/lib/nagios/plugins/check_prometheus
    mode: "0775"

# This is to allow the nagios user to read pod statuses
- name: "Copying kubernetes config file"
  ansible.builtin.copy:
    src: "/etc/kubernetes/admin.conf"
    dest: "/etc/icinga2/kubernetes.conf"
    owner: nagios
    group: nagios
    mode: "0600"
    remote_src: true
