---
- name: Remove Icinga2 repositories that don't reference the correct key
  # See: https://icinga.com/blog/2024/08/26/icinga-package-repository-key-rotation-2024/
  ansible.builtin.apt_repository:
    update_cache: false
    state: absent
    filename: "{{ ansible_distribution_release }}-icinga"
    repo: "{{ item }}"
  loop:
    # these are all the old repositories that don't reference the correct key
    - deb {{ icinga2_repo_url }} icinga-{{ ansible_distribution_release }} main
    - deb-src {{ icinga2_repo_url }} icinga-{{ ansible_distribution_release }} main
    - deb [signed-by=/usr/share/keyrings/icinga-archive-keyring.gpg] https://packages.icinga.com/ubuntu icinga-jammy main
    - |
      deb {{ icinga2_repo_url }} icinga-{{ ansible_distribution_release }} main
      deb-src {{ icinga2_repo_url }} icinga-{{ ansible_distribution_release }} main

- name: Remove Icinga2 repository file
  # Not sure why this file exists but it causes conflicts because the
  # file created by this role is named "jammy-icinga.list"
  ansible.builtin.file:
    state: absent
    path: /etc/apt/sources.list.d/icinga.list

- name: Installing dependencies
  ansible.builtin.apt:
    update-cache: yes
    name: "{{ packages }}"
  vars:
    packages:
    - apt-transport-https
    - gnupg
    state: present

- name: Download the Icinga GPG key and save it to the keyrings directory
  ansible.builtin.get_url:
    url: "{{ icinga2_keyring_url }}"
    dest: "{{ icinga2_keyring_dest }}"
    mode: '0644'

- name: Adding icinga2 repository
  ansible.builtin.apt_repository:
    filename: "{{ ansible_distribution_release }}-icinga"
    repo: "{{ icinga2_apt_repository }}"

- name: "Logging OS distribution"
  ansible.builtin.debug:
    msg: "{{ ansible_distribution_release }}"

- name: Setting icinga2-bin package version
  ansible.builtin.set_fact:
    icinga2_package_version: |
      {{ '2.13.3-1.bionic' if ansible_distribution_release == 'bionic' else icinga2_package_version }}

- name: Remove existing Icinga2 packages
  ansible.builtin.apt:
    name:
      - icinga2
      - icinga2-bin
      - icinga2-common
      - icinga2-doc
    state: absent
  when: icinga_force_setup | default(false)

- name: Installing Icinga2 and dependencies
  ansible.builtin.apt:
    name:
      - icinga2={{ icinga2_package_version }}
      - icinga2-bin={{ icinga2_package_version }}
      - icinga2-common={{ icinga2_package_version }}
    state: present
    update_cache: yes
    allow_downgrade: yes
  register: icinga2_install_result
  retries: 3
  delay: 5
  until: icinga2_install_result is success

- name: Excluding conf.d directory
  ansible.builtin.lineinfile:
    dest: /etc/icinga2/icinga2.conf
    regexp: 'conf.d'
    line: '#include_recursive "conf.d"'
