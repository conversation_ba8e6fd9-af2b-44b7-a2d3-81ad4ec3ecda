---
- name: Copying uptime check script
  ansible.builtin.copy: 
    src: check_uptime
    dest: /usr/lib/nagios/plugins
    owner: root
    group: root
    mode: "+x"

- name: Installing needrestart check
  ansible.builtin.include_tasks: needrestart.yml

- name: Installing cameras check
  ansible.builtin.include_tasks: cameras.yml
  when: icinga2_deploy_cameras_check

- name: Installing serial number check
  ansible.builtin.include_tasks: serial_number.yml
  when: icinga2_deploy_serialnumber_check

- name: Installing Kubernetes check
  ansible.builtin.include_tasks: check_k8s.yml
  when: icinga2_deploy_kubernetes_check

- name: Installing Prometheus check
  ansible.builtin.include_tasks: check_prometheus.yml
  when: icinga2_deploy_prometheus_check

- name: Installing cvescan check
  ansible.builtin.include_tasks: cvescan.yml

- name: Update visudo file
  ansible.builtin.include_tasks: visudo.yml
