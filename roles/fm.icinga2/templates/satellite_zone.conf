object Endpoint "icinga2-master.infra.fingermark.tech" {
	host = "api.icinga2-master.infra.fingermark.tech"
	port = 5665
}

object Zone "master" {
	endpoints = [ "icinga2-master.infra.fingermark.tech" ]
}

object Endpoint "icinga2-satellite.{{ icinga2_satellite_zone_name }}.infra.fingermark.tech" {
}

object Zone "{{ icinga2_satellite_zone_name }}" {
	endpoints = [ "icinga2-satellite.{{ icinga2_satellite_zone_name }}.infra.fingermark.tech" ]
	parent = "master"
}

object Zone "global-templates" {
	global = true
}

object Zone "director-global" {
	global = true
}