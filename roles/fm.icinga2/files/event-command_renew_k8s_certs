#!/bin/bash

#From 1.20 onwards dont use alpha

renew_kubeadm_certificates() {
    k8s_version=$(kubectl version --short | awk -F: '/Client Version/{print $NF}' | tr -d '[:space:]' | tr -d 'v')


    
    if [[ "$(echo "$k8s_version" | awk -F. '{print $1}')" -ge 1 && "$(echo "$k8s_version" | awk -F. '{print $2}')" -ge 20 ]]; then
        kubeadm certs renew all
    else
        kubeadm alpha certs renew all
    fi
}

main() {
    if renew_kubeadm_certificates; then
        echo "Certificates renewed successfully."
    else
        echo "Error renewing certificates: $?"
    fi
}

main