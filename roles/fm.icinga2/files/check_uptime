#!/bin/bash

UPTIME=$(cat /proc/uptime | awk '{print $1}' | cut -d '.' -f1)

if [[ "$UPTIME" -lt 60 ]]; then
  echo "WARNING! Uptime is $UPTIME seconds."
  exit 1
else
  if [[ "$UPTIME" -lt 3600 ]]; then
    echo "WARNING! Uptime is $((UPTIME/60)) minutes."
    exit 1
  else
    if [[ "$UPTIME" -lt 86400 ]]; then
      echo "OK! Uptime is $((UPTIME/3600)) hours."
    else
      echo "OK. Uptime is $((UPTIME/86400)) days"
    fi
    exit 0
  fi
fi
