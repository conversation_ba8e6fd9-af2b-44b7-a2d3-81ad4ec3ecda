#!/usr/bin/env python3
from kubernetes import client, config
import sys

KUBE_CONFIG="/etc/icinga2/kubernetes.conf"

def get_pod_info(search_string):
    config.load_kube_config(KUBE_CONFIG)  # Load kubeconfig from default location (~/.kube/config)

    v1 = client.CoreV1Api()
    pods = v1.list_pod_for_all_namespaces().items
    
    for pod in pods:
        if search_string.lower() in pod.metadata.name.lower():
            return pod.metadata.name, pod.metadata.namespace
    
    return None, None


# Function to get the pod's health status
def get_pod_health():
    config.load_kube_config(KUBE_CONFIG)  # Load kubeconfig from default location (~/.kube/config)

    v1 = client.CoreV1Api()
    pod = v1.read_namespaced_pod(name=pod_name, namespace=namespace)

    # Check the pod's conditions for health status
    ready_condition = next((c for c in pod.status.conditions if c.type == "Ready"), None)
    containers_ready_condition = next((c for c in pod.status.conditions if c.type == "ContainersReady"), None)
    status_running = pod.status.phase == "Running"

    return ready_condition.status == "True" and containers_ready_condition.status == "True" and status_running

# Function to check the health conditions and return appropriate status codes
def check_health_conditions(healthy):
    if healthy:
        return 0  # OK
    else:
        return 2  # CRITICAL

if __name__ == '__main__':
    try:
        search_string = "prometheus-monitor"
        pod_name, namespace = get_pod_info(search_string)
        healthy = get_pod_health()
        print(f"{'Healthy' if healthy else 'Unhealthy'}")

        status_code = check_health_conditions(healthy)
        sys.exit(status_code)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(4)

