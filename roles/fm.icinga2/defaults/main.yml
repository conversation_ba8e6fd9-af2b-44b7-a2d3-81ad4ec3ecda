---
icinga2_master_node: "icinga2-master.infra.fingermark.tech"
icinga2_satellite_endpoint: "icinga2-satellite.{{ customer }}.infra.fingermark.tech"
icinga2_satellite_zone_name: "{{ customer }}"
icinga2_deploy_cameras_check: false
icinga2_deploy_serialnumber_check: false
icinga2_deploy_kubernetes_check: false
icinga2_deploy_prometheus_check: false
icinga2_auto_config_passwd: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          31346663336165623561363134346233343565663038373536303232373466306439306162383162
          3836316261336631346430316634386333336639653835390a333835653634613534633037343061
          38656237373633313532343432383164323366323165313163373131323935623264383964306232
          3265666135626566330a396564623730623439313733623531623337376632643336366562353862
          6232
icinga2_run_setup: false
icinga2_master_setup: false
icinga2_install_db: yes
icinga2_node_setup: false
icinga2_satellite_setup: false
icinga2_master_api_endpoint: "api.icinga2-master.infra.fingermark.tech"
maintenance_window: '0 3 1-7 * *   root  test $(/usr/bin/date +\%u) -eq 2 && /sbin/reboot'
coordinates: "0,0"
icinga2_cert_path: "/var/lib/icinga2/ca"
icinga2_setup_file_stat: "/etc/icinga2/node_setup"
icinga2_package_version: 2.14.3-1+ubuntu22.04
icinga2_repo_url: https://packages.icinga.com/ubuntu
icinga2_keyring_url: https://packages.icinga.com/icinga.key
icinga2_keyring_dest: /usr/share/keyrings/icinga-archive-keyring.asc
icinga2_apt_arch: '{{ (ansible_architecture == "aarch64") | ternary("arm64", "amd64") }}'
icinga2_apt_repository: "deb [arch={{ icinga2_apt_arch }}, signed-by={{ icinga2_keyring_dest }}] {{ icinga2_repo_url }} icinga-{{ ansible_distribution_release }} main"
