# zfm.icinga2


Role for installing Icinga2 Monitoring client https://icinga.com/



## Example Playbook


Including an example of how to use your role (for instance, with variables passed in as parameters) is always nice for users too:

    - hosts: servers
      roles:
         - fm.icinga2

## Adding New Custom Checks

Official docs: https://icinga.com/docs/icinga-2/latest/doc/05-service-monitoring/#new-checkcommand

- Create the check command file that will perform the check. The file name should be check_\<metric\>
- Create the CheckCommand definition file for your plugin: https://icinga.com/docs/icinga-2/latest/doc/05-service-monitoring/#checkcommand-definition
  - The file should be created under /etc/icinga2/zones.d/global-templates/commands.conf
    ```
    object CheckCommand "needrestart" {
      import "plugin-check-command"
      command = [ PluginDir + "/check_needrestart" ]
      arguments += {}
    }

    ```
- Create a new services in /etc/icinga2/zones.d/global-templates/services.conf
- Check the configuration by running
  ```
  sudo icinga2 daemon -C


  ```