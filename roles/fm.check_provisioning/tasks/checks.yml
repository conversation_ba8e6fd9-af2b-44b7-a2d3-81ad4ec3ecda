---
- name: "Checking VPN connection"
  ansible.builtin.command: ping -4 -c 2 {{ icinga2_master_ip }}
  register: ping_result
  ignore_errors: true

- name: "Checking nvidia drivers"
  ansible.builtin.command: nvidia-smi
  register: nvidia_smi_output
  ignore_errors: true

- ansible.builtin.debug:
    msg: "{{ nvidia_smi_output.stdout_lines[2] }}"
  when: not nvidia_smi_output.failed

- name: "Checking Teleport service"
  ansible.builtin.systemd:
    name: teleport
    state: started
  ignore_errors: true
  register: teleport_output

- ansible.builtin.debug:
    msg: "Teleport is {{ teleport_output.status.ActiveState | default('') }} since {{ teleport_output.status.ActiveEnterTimestamp | default('') }}"
  when: not teleport_output.failed

- name: Getting UUID
  ansible.builtin.shell: "sudo dmidecode --type system | grep 'UUID' | sed 's/\tUUID: //'"
  register: uuid_result
  ignore_errors: true

- ansible.builtin.set_fact:
    uuid_result={{ uuid_result.stdout_lines }}

- ansible.builtin.debug:
    msg: "UUID: {{ uuid_result }}"

- name: Secure boot check
  ansible.builtin.shell: "mokutil --sb-state"
  register: secure_boot_result
  ignore_errors: true

- name: Secure boot check debug
  ansible.builtin.debug:
    msg: "mokutil: stdout={{ secure_boot_result.stdout | default('(empty)') }} \n stderr={{ secure_boot_result.stderr | default('(empty)') }}"

- name: Serial number
  ansible.builtin.shell: "sudo dmidecode -s system-serial-number"
  register: serial_number_result
  ignore_errors: true

- name: Serial number debug
  ansible.builtin.debug:
    msg: "serial_number={{ serial_number_result.stdout | default('(empty)') }}"

- name: Kubernetes check 01/02
  kubernetes.core.k8s_cluster_info:
    kubeconfig: "{{ kubeconfig_location }}"
  ignore_errors: true
  register: k8s_api_status

- name: Kubernetes check 02/02
  ansible.builtin.shell: systemctl status kubelet
  ignore_errors: true
  register: kubelet_output

- name: Icinga2 check
  ansible.builtin.systemd:
    name: icinga2
    state: started
  ignore_errors: true  
  register: icinga2_output

- name: Check which device is mounted at /media/fingermark/storage
  ansible.builtin.command: findmnt -n -o SOURCE --target /media/fingermark/storage
  register: findmnt_result
  ignore_errors: true
  # TODO: We will need to ignore these for HP servers with single disk

- name: Set storage_mounted fact
  ansible.builtin.set_fact:
    storage_mounted: "{{ findmnt_result.rc == 0 }}"
