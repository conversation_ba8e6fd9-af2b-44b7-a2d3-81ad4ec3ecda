---


- name: Disable system swap
  shell: "swapoff -a"

- name: Remove current swaps from fstab
  lineinfile:
    dest: /etc/fstab
    regexp: '(?i)^([^#][\S]+\s+(none|swap)\s+swap.*)'
    line: '# \1'
    backrefs: yes
    state: present

- name: Ensure br_netfilter is enabled.
  modprobe:
    name: br_netfilter
    state: present

- name: Disable swappiness and pass bridged IPv4 traffic to iptable's chains
  sysctl:
    name: "{{ item.name }}"
    value: "{{ item.value }}"
    state: present
  with_items:
    - { name: 'vm.swappiness', value: '0' }
    - { name: 'net.bridge.bridge-nf-call-iptables', value: '1' }


# - name: Install Kubernetes packages
#   include_tasks: pkg.yml

# - name: Copy kubeadm conf to drop-in directory
#   template: src=20-extra-args.conf.j2 dest=/etc/systemd/system/kubelet.service.d/20-extra-args.conf

# - name: Reload kubelet daemon
#   systemd:
#     name: kubelet
#     daemon_reload: yes
#     enabled: yes
