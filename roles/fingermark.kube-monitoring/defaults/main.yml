---
kubectl: "kubectl --kubeconfig {{ kubeconfig_location }}"
kubeconfig_location: "/etc/kubernetes/admin.conf"
monitoring_ns: monitoring
graylog_server: **************
fluent_bit_version: 1.7.1
chart_version: "45.7.1"
prometheus_operator_version: "0.55.0"
prometheus_operator_uninstall: no
prometheus_operator_present: true
thanos_release: 0.24.0
thanos_release_packet: thanos-{{ thanos_release }}.linux-amd64.tar.gz
thanos_use_obj_storage: no
thanos_upgrade: no
thanos_sidecar_install: no
helm_stable_repo: https://charts.helm.sh/stable
node_exporter_standalone_version: no
delete_logging_ns: no
dcgm_exporter_namespace: "nmp-{{ ansible_hostname }}"
microk8s: no
force_deploy_new_dcgm_exporter: no # works with prometheus_operator_uninstall flag
dcgm_exporter_image_tag: "2.1.4-2.3.1-ubuntu18.04"
rabbitmq_exporter_chart_version: 0.5.3
prometheus_chart_version: 38.0.2
prom_setup_node_exporter: true
prom_enable_kube_api_server: true
prom_enable_kubelet: true
prom_enable_kube_controller_manager: true
prom_enable_kube_etcd: true
prom_enable_kube_scheduler: true
prom_enable_kube_state_metrics: true
prom_enable_alert_manager: true
prom_enable_coredns: true
prom_enable_kube_proxy: true
prom_use_pv: true
prom_storage_mount_dir: /media/fingermark/storage/prometheus
prom_storageclass_name: local-storage
prom_storage_capacity: 50Gi
prom_pv_name: pv-prometheus-operator-prometheus-0
prom_release_values:
  # https://github.com/prometheus-community/helm-charts/blob/main/charts/kube-prometheus-stack/values.yaml
  grafana:
    enabled: false
  nodeExporter:
    enabled: "{{ prom_setup_node_exporter }}"
  alertmanager:
    enabled: "{{ prom_enable_alert_manager }}"
  coreDns:
    enabled: "{{ prom_enable_coredns }}"
  kubeApiServer:
    enabled: "{{ prom_enable_kube_api_server }}"
  kubelet:
    enabled: "{{ prom_enable_kubelet }}"
  kubeControllerManager:
    enabled: "{{ prom_enable_kube_controller_manager }}"
  kubeProxy:
    enabled: "{{ prom_enable_kube_proxy }}"
  kubeEtcd:
    enabled: "{{ prom_enable_kube_etcd }}"
  kubeScheduler:
    enabled: "{{ prom_enable_kube_scheduler }}"
  kubeStateMetrics:
    enabled: "{{ prom_enable_kube_state_metrics }}"
  prometheusOperator:
    kubeletService:
      enabled: "{{ prom_enable_kubelet }}"
  prometheus:
    server:
      persistentVolume:
        enabled: true
    service:
      type: NodePort
    prometheusSpec:
      externalUrl: "{{ ansible_hostname }}.eyeq.vpn:30090"
      externalLabels:
        cluster: "{{ ansible_hostname }}"
      securityContext:
        runAsNonRoot: false
        runAsUser: 0
        fsGroup: 0
      storageSpec:
        volumeClaimTemplate:
          metadata:
            name: "{{ prom_pv_name }}"
          spec:
            accessModes: ["ReadWriteOnce"]
            resources:
              requests:
                storage: "{{ prom_storage_capacity }}"
            selector:
              matchLabels:
                name: "{{ prom_pv_name }}"
            # storageClassName: "{{ prom_storageclass_name }}"
