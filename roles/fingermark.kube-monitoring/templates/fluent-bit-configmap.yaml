apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
  namespace: logging
  labels:
    k8s-app: fluent-bit
data:
  # Configuration files: server, input, filters and output
  # ======================================================
  fluent-bit.conf: |
    [SERVICE]
        Flush         5
        Log_Level     error
        Daemon        off
        Parsers_File  parsers.conf
        HTTP_Server   On
        HTTP_Listen   0.0.0.0
        HTTP_Port     2020

    @INCLUDE input-kubernetes.conf
    @INCLUDE filter-kubernetes.conf
    @INCLUDE output-graylog.conf

  input-kubernetes.conf: |
    [INPUT]
        Name              tail
        Tag               kube.*        
        Path              /var/log/containers/*.log
        Exclude_Path      /var/log/containers/kube-*, /var/log/containers/calico*, /var/log/containers/alert*, /var/log/containers/coredns*
        Parser            docker
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     5MB
        Skip_Long_Lines   On
        Refresh_Interval  10        

  filter-kubernetes.conf: |
    [FILTER]
        Name                kubernetes
        Match               kube.*
        Kube_URL            https://kubernetes.default.svc.cluster.local:443
        Merge_Log           Off
        K8S-Logging.Parser  On

    # ${HOSTNAME} returns the host name.
    # But Fluentbit runs in a container. So, it is not meaningful.
    # Instead, copy the host name from the Kubernetes object.
    [FILTER]
        Name nest
        Match *
        Operation lift
        Nested_under kubernetes

    # Remove offending fields, see: https://github.com/fluent/fluent-bit/issues/1291
    [FILTER]
        Name record_modifier
        Match *
        Remove_key annotations
        Remove_key labels


  output-graylog.conf: |
    [OUTPUT]
        Name          gelf
        Match         *
        Host          {{ graylog_server }}
        Port          12201
        Mode          tcp
        Gelf_Short_Message_Key log

  parsers.conf: |
    [PARSER]
        Name   json
        Format json
        Time_Key time
        Time_Format %Y-%m-%dT%H:%M:%S.%LZ

    [PARSER]
        Name        docker
        Format      json
        Time_Key    time
        Time_Format %Y-%m-%dT%H:%M:%S.%LZ
        Time_Keep   On


