---
## Delete me using:
## kubectl delete pv pv-prometheus-operator-prometheus-0 --force
## kubectl patch pv pv-prometheus-operator-prometheus-0 -p '{"metadata": {"finalizers": null}}'
apiVersion: v1
kind: PersistentVolume
metadata:
  name: "{{ prom_pv_name }}"
  labels:
    name: "{{ prom_pv_name }}"
spec:
  capacity:
    storage: "{{ prom_storage_capacity }}"
  # storageClassName: "{{ prom_storageclass_name }}"
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "{{ prom_storage_mount_dir }}"
