- name: Removing Prometheus Operator
  kubernetes.core.helm:
    name: monitor
    state: absent
    wait: true
    kubeconfig: "{{ kubeconfig_location }}"
    release_namespace: "{{ monitoring_ns }}"
  ignore_errors: true

- name: Delete ValidatingWebhookConfiguration Webhook
  kubernetes.core.k8s:
    api_version: v1
    kind: ValidatingWebhookConfiguration
    name: monitor-kube-prometheus-st-admission
    state: absent
    kubeconfig: "{{ kubeconfig_location }}"

- name: Delete Prometheus CRDs
  kubernetes.core.k8s:
    api_version: "apiextensions.k8s.io/v1"
    kind: "CustomResourceDefinition"
    name: "{{ item }}"
    state: absent
    kubeconfig: "{{ kubeconfig_location }}"
  with_items: 
    - alertmanagerconfigs.monitoring.coreos.com
    - alertmanagers.monitoring.coreos.com
    - podmonitors.monitoring.coreos.com
    - probes.monitoring.coreos.com
    - prometheuses.monitoring.coreos.com
    - prometheusrules.monitoring.coreos.com
    - servicemonitors.monitoring.coreos.com
    - thanosrulers.monitoring.coreos.com
  ignore_errors: true

- name: Delete Prometheus Cluster Roles
  kubernetes.core.k8s:
    # api_version: "apiextensions.k8s.io/v1"
    kind: "ClusterRole"
    name: "{{ item }}"
    state: absent
    kubeconfig: "{{ kubeconfig_location }}"
  with_items: 
    - monitor-kube-prometheus-st-admission
    - monitor-kube-prometheus-st-operator
    - monitor-kube-prometheus-st-prometheus
    - monitor-kube-state-metric
  ignore_errors: true

- name: Delete Prometheus Cluster Roles Bindings
  kubernetes.core.k8s:
    # api_version: "apiextensions.k8s.io/v1"
    kind: "ClusterRoleBinding"
    name: "{{ item }}"
    state: absent
    kubeconfig: "{{ kubeconfig_location }}"
  with_items: 
    - monitor-kube-state-metrics
    - monitor-kube-prometheus-st-admission
    - monitor-kube-prometheus-st-operator
    - monitor-kube-prometheus-st-prometheus
  ignore_errors: true

- name: Delete Prometheus kube-system services
  kubernetes.core.k8s:
    # api_version: "apiextensions.k8s.io/v1"
    kind: "Service"
    namespace: kube-system
    name: "{{ item }}"
    state: absent
    kubeconfig: "{{ kubeconfig_location }}"
  with_items: 
    - monitor-kube-prometheus-st-coredns
    - monitor-kube-prometheus-st-kube-etcd
    - monitor-kube-prometheus-st-kube-controller-manager
    - monitor-kube-prometheus-st-kube-proxy
    - monitor-kube-prometheus-st-kube-scheduler
  ignore_errors: true

# Release the PV to be used again
- name: Releasing persisting volume
  kubernetes.core.k8s:
    state: patched
    kind: "PersistentVolume"
    name: "{{ prom_pv_name }}"
    kubeconfig: "{{ kubeconfig_location }}"
    definition:
      spec:
        claimRef: null

- name: Delete Persistent Volume Claim
  kubernetes.core.k8s:
    kubeconfig: "{{ kubeconfig_location }}"
    namespace: "{{ monitoring_ns }}"
    api_version: v1
    kind: PersistentVolumeClaim
    name: "{{ item }}"
    state: absent
  with_items:
    - "{{ prom_pv_name }}-prometheus-monitor-kube-prometheus-st-prometheus-0"
    - prometheus-monitor-kube-prometheus-st-prometheus-db-prometheus-monitor-kube-prometheus-st-prometheus-0

- name: Delete Persistent Volume 
  kubernetes.core.k8s:
    kubeconfig: "{{ kubeconfig_location }}"
    api_version: v1
    kind: PersistentVolume
    name: "{{ prom_pv_name }}"
    state: absent