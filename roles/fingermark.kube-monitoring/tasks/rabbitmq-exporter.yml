---
# These tasks used to create the RabbitMQ exporter however, the exporter is no longer needed
# as the we are scraping the RabbitMQ metrics directly from the RabbitMQ pod.
- name: Uninstall RabbitMQ exporter
  kubernetes.core.helm:
    release_state: absent
    release_name: rabbitmq-exporter
    release_namespace: nmp-{{ ansible_hostname }}
    chart_ref: stable/prometheus-rabbitmq-exporter
    create_namespace: yes
    update_repo_cache: yes
    chart_version: "{{ rabbitmq_exporter_chart_version }}"
    kubeconfig: "{{ kubeconfig_location }}"
    values:
      rabbitmq:
        url: http://rabbitmq:15672

- name: Uninstall RabbitMQ ServiceMonitor
  kubernetes.core.k8s:
    state: absent
    definition: "{{lookup('template', 'rabbitmq-service-monitor.yml')}}"
    kubeconfig: "{{ kubeconfig_location }}"
