---
# https://docs.ansible.com/ansible/latest/collections/kubernetes/core/helm_module.html#requirements

- name: Create a k8s namespace
  kubernetes.core.k8s:
    name: monitoring
    api_version: v1
    kind: Namespace
    state: present
    kubeconfig: "{{ kubeconfig_location }}"

- name: Installing Helm Chart Prometheus' Repository
  kubernetes.core.helm_repository:
    name: prometheus-community
    repo_url: "https://prometheus-community.github.io/helm-charts"

- name: Installing Helm Chart Official Repository
  kubernetes.core.helm_repository:
    name: stable
    repo_url: "{{ helm_stable_repo }}"

# Force deleting the monitoring namespace would result in some webhooks to be prevailing which would cause an error on re-installation.
# Use helm for a clean removal of the monitor or else look out for the following webhooks:
# kubectl delete MutatingWebhookConfiguration monitor-kube-prometheus-st-admission
# kubectl delete validatingwebhookconfigurations.admissionregistration.k8s.io monitor-kube-prometheus-st-admission

- name: Creating persistent volumes
  kubernetes.core.k8s:
    state: present
    kubeconfig: "{{ kubeconfig_location }}"
    definition: "{{ lookup('template', 'storage.yml') | from_yaml_all }}"
  when: prom_use_pv

- name: Creating directory to mount
  ansible.builtin.file:
    path: "{{ prom_storage_mount_dir }}"
    state: directory
    recurse: yes
    owner: root
    group: root
  when: prom_use_pv

- name: Install Prometheus-Operator
  kubernetes.core.helm:
    timeout: 15m0s # increase helm module timeout in case image pulls take longer.
    release_name: monitor
    release_namespace: "{{ monitoring_ns }}"
    chart_ref: prometheus-community/kube-prometheus-stack
    chart_version: "{{ prometheus_chart_version }}"
    create_namespace: yes
    update_repo_cache: yes
    kubeconfig: "{{ kubeconfig_location }}"
    release_values: "{{ prom_release_values | from_yaml }}"
    wait: true

- name: Creating Fingermark Pod Status PromRule
  kubernetes.core.k8s:
    state: present
    definition: "{{lookup('file', 'fingermark-pod-status-promrule.yml')}}"
    kubeconfig: "{{ kubeconfig_location }}"
