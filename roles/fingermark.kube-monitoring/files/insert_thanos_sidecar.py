import yaml

with open("/tmp/thanos_sidecar.yaml", "r") as thanos_stream:
    try:
        thanos = yaml.safe_load(thanos_stream)
    except:
        print("Error reading Thanos sidecar file")

thanos_stream.close()

with open("/tmp/prometheus.yaml", "r") as prom_stream:
    try:
        prometheus = yaml.safe_load(prom_stream)                
        prometheus["items"][0]["apiVersion"]
        prometheus["items"][0]["spec"]["thanos"] = thanos["thanos"]
        prometheus["items"][0]["spec"]["externalLabels"] = thanos["externalLabels"]
        output = yaml.dump(prometheus)
        print(output)
            
    except:
        print("Error reading Prometheus file")

prom_stream.close()

with open("/tmp/output.yaml", "w+") as output_file:
    try:
        output_file.write(output)
    except:
        print("Couldn't create output file")

output_file.close()