apiVersion: v1
kind: Service
metadata:
  labels:
    prometheus: monitor-prometheus-operato-prometheus
  name: thanos-sidecar-svc
  namespace: monitoring
spec:
  type: LoadBalancer
  ports:
  - name: web
    #port: 9090
    port: 10902
    targetPort: web
  - name: grpc
    port: 10901
    targetPort: 10901
    nodePort: 30901

  selector:
    app: prometheus    
    # prometheus: monitor-prometheus-operato-prometheus