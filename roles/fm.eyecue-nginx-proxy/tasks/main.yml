---
- name: "Pulling nginx"
  shell: docker pull {{ item }}
  with_items:
  - "nginx:1.19.2"

- name: Create a nginx directory
  file:
    path: /opt/nginx/
    state: directory
    mode: '0755'

- name: Copy nginx config to server
  copy:
    src: nginx.conf
    dest: /opt/nginx/proxy.conf

- name: "Run nginx"
  shell: docker run -d --net host -v /opt/nginx/proxy.conf:/etc/nginx/conf.d/default.conf:ro nginx 