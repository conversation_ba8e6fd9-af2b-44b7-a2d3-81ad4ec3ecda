#!/usr/sbin/nft -f

flush ruleset

table inet filter {
	chain input {
		type filter hook input priority 0; policy drop;
		ct state invalid drop;
		ct state {established, related} accept;
		iif lo accept comment "accept loopback";
		icmp type echo-request accept;
		iifname lo accept;
		tcp dport {80,443,10250,10256,6443,10902,10901} accept;
		ip saddr **************/32 tcp dport {3021,3022,3023,3024,3080} accept;
		tcp dport {30080,32691,30003,32000,30676,30672,32568,30338,30029,30090,32500} accept;
		iifname vpn_sfteth ip saddr ************/22 tcp dport 22 accept;
		iifname vpn_mcd ip saddr ************/22 tcp dport 22 accept;
	}
	chain forward {
		type filter hook forward priority 0;
	}
	chain output {
		type filter hook output priority 0;
	}
}