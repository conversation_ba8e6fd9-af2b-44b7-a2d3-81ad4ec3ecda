ArgoCD Role
=========

Install argocd gitops tool and create Eyecue application

Requirements
------------

Eyecue Server must been already setup. First run 
    ```ansible-playbook playbooks/k8s-cv-server.yml ```
if you haven't done.


Dependencies
------------

A list of other roles hosted on Galaxy should go here, plus any details in regards to parameters that may need to be set for other roles, or variables that are used from other roles.

Example Playbook
----------------

Including an example of how to use your role (for instance, with variables passed in as parameters) is always nice for users too:

    - name: Install Argocd  remotely
      gather_facts: yes
      become: yes
      hosts: fm_mcd_nz_test
      roles:
      - argocd

Write down the vault password in roles/argocd/files/password.txt

``` ansible-playbook playbooks/argocd.yaml --vault-password-file roles/argocd/files/password.txt ```

License
-------

BSD


