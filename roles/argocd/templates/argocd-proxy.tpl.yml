apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/component: s3-proxy
    app.kubernetes.io/name: argocd-s3-proxy
    app.kubernetes.io/part-of: argocd
  ###### TODO ######
  #  istio-injection: enabled
  ##################
  namespace: argocd
  name: argocd-s3-proxy
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: argocd-s3-proxy
  template:
    metadata:
      labels:
        app.kubernetes.io/name: argocd-s3-proxy
    spec:
      containers:
      - name: s3-proxy
        image: pottava/s3-proxy:2.0
        env:
          - name: AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                name: helm-repo-reader
                key: AWS_ACCESS_KEY_ID
          - name: AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: helm-repo-reader
                key: AWS_SECRET_ACCESS_KEY
          - name: AWS_REGION
            value: {{ helm_bucket_default_region }}
          - name: AWS_S3_BUCKET
            value: {{ helm_bucket_name }}
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: 50Mi
            cpu: 10m
          limits:
            memory: 100Mi
            cpu: 50m
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/component: s3-proxy
    app.kubernetes.io/name: argocd-s3-proxy
    app.kubernetes.io/part-of: argocd
  name: argocd-s3-proxy
  namespace: argocd
spec:
  ports:
  - name: server
    port: 80
    protocol: TCP
    targetPort: 80
  selector:
    app.kubernetes.io/name: argocd-s3-proxy
