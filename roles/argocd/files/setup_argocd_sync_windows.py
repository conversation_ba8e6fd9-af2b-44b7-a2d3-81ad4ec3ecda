#!/usr/bin/python3

import time
import subprocess

def convert_to_utc(hour: int):
    utc_offset = time.localtime().tm_hour - time.gmtime().tm_hour
    time_with_offset = (24 - utc_offset + hour) % 24
    return time_with_offset

def set_argocd_sync_window(schedule: str):
    try:
        subprocess.run(["argocd", "proj", "windows", "add", "default", "--kind", "allow", "--schedule", schedule, "--duration", "1h", "--applications", "*"])
        print("Sync windows, configured")
    except Exception as e:
        print(e)

if __name__ == "__main__":
    sync_time = 4 # 4:00AM
    
    cron_notation = "* " +  str(convert_to_utc(sync_time)) + " * * *"
    
    set_argocd_sync_window(cron_notation)