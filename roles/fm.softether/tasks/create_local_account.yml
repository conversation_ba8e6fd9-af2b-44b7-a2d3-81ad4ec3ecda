---

- name: Ensuring <PERSON>eth<PERSON> is running
  command: vpnclient start
  register: vpnclient_status

- name: "Checking if NIC is already present"
  command: "{{ softether_dest_dir }}/vpnclient/vpncmd /CLIENT localhost /CMD NICENABLE {{ softether_client_vpn_nic_name }}"
  no_log: yes
  register: nic_status
  ignore_errors: true

- debug:
    msg: "Virtual NIC is present. It won't be created"
  when: "not nic_status.failed"

- name: Creating Virtual NIC
  command: "{{ softether_dest_dir }}/vpnclient/vpncmd /CLIENT localhost /CMD NICCREATE {{ softether_client_vpn_nic_name }}"
  when: "nic_status.failed"

- name: Checking if local account exits
  command: "{{ softether_dest_dir }}/vpnclient/vpncmd /CLIENT localhost /CMD ACCOUNTGET {{ softether_username }} "
  no_log: yes
  register: account_list
  ignore_errors: true

- debug:
    msg: "The user account {{ softether_username }} does not exists"
  when: "account_list.failed"

- debug:
    msg: "The user account {{ softether_username }} already exists"
  when: "not account_list.failed" 

- name: Creating User Account
  command: "{{ softether_dest_dir }}/vpnclient/vpncmd /CLIENT localhost /CMD ACCOUNTCREATE {{ softether_username }} /SERVER:{{ softether_server_ip }}:{{ softether_port_number }} /HUB:{{ softether_virtualhub | trim }} /USERNAME:{{ softether_username }} /NICNAME:{{ softether_client_vpn_nic_name }}"
  when: account_list.failed and nic_status.stdout_lines[1].find(ansible_hostname)

- name: Setting up User Account Password
  command: "{{ softether_dest_dir }}/vpnclient/vpncmd /CLIENT localhost /CMD ACCOUNTPASSWORDSET {{ softether_username }} /PASSWORD:{{ vpn_user_password }} /TYPE:standard"
  ignore_errors: true

- name: Setting up Autoconnect Option
  command: "{{ softether_dest_dir }}/vpnclient/vpncmd /CLIENT localhost /CMD ACCOUNTSTARTUPSET {{ softether_username }}"

- name: Connecting User Account
  command: "{{ softether_dest_dir }}/vpnclient/vpncmd /CLIENT localhost /CMD ACCOUNTCONNECT {{ softether_username }}"
  ignore_errors: true

