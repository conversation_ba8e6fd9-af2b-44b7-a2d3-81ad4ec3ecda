---
- name: "This playbook removes Prometheus installed by <PERSON><PERSON>"
  become: true
  
  hosts: "{{ run_hosts.split(',') }}"

  collections:
    - kubernetes.core
  vars:
    run_hosts: "localhost"
    kubeconfig_location: "/etc/kubernetes/admin.conf"
    monitoring_ns: "monitoring"

  tasks:
    - name: Removing Prometheus Operator
      kubernetes.core.helm:
        name: monitor
        state: absent
        wait: true
        kubeconfig: "{{ kubeconfig_location }}"
        release_namespace: "{{ monitoring_ns }}"
      ignore_errors: true

    - name: Delete ValidatingWebhookConfiguration Webhook
      kubernetes.core.k8s:
        api_version: v1
        kind: ValidatingWebhookConfiguration
        name: monitor-kube-prometheus-st-admission
        state: absent
        kubeconfig: "{{ kubeconfig_location }}"

    - name: Delete Prometheus CRDs
      kubernetes.core.k8s:
        api_version: "apiextensions.k8s.io/v1"
        kind: "CustomResourceDefinition"
        name: "{{ item }}"
        state: absent
        kubeconfig: "{{ kubeconfig_location }}"
      with_items:
        - alertmanagerconfigs.monitoring.coreos.com
        - alertmanagers.monitoring.coreos.com
        - podmonitors.monitoring.coreos.com
        - probes.monitoring.coreos.com
        - prometheuses.monitoring.coreos.com
        - prometheusrules.monitoring.coreos.com
        - servicemonitors.monitoring.coreos.com
        - thanosrulers.monitoring.coreos.com
      ignore_errors: true