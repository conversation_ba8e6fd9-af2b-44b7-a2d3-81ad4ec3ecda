---
- name: Remove non-emergency users
  become: yes
  hosts:
  - test

  vars:
    emergency_users:
      - awx
      - campbell
      - sam
      - jacques
      - stan.mans
      - mark
      - fingermark
      - ssm-user

  tasks:
    - name: Get list of non-system users
      shell: "getent passwd | awk -F: '$3 >= 1000 && $3 < 65534 {print $1}'"
      register: all_users

    - name: Remove non-emergency users
      user:
        name: "{{ item }}"
        state: absent
        remove: yes
      loop: "{{ all_users.stdout_lines | difference(emergency_users) }}"

    - name: Re-create emergency users
      ansible.builtin.import_role:
        name: fm.users
