---
- name: Audit external disk and storage mount status
  hosts: "{{ run_hosts.split(',') }}"

  become: true
  gather_facts: false

  vars:
    storage_mount_path: /media/fingermark/storage

  tasks:
    - name: Check if storage mount is active and get its source device
      ansible.builtin.command: findmnt -n -o SOURCE "{{ storage_mount_path }}"
      register: findmnt_result
      changed_when: false
      failed_when: false

    - name: Set mount status facts
      ansible.builtin.set_fact:
        storage_mounted: "{{ findmnt_result.rc == 0 }}"
        mounted_device: >-
          {% if findmnt_result.rc == 0 %}
            {{ findmnt_result.stdout | trim }}
          {% else %}
            n/a
          {% endif %}

    - name: Get lsblk output as JSON
      ansible.builtin.command: lsblk -b -J -o NAME,TYPE,SIZE
      register: lsblk_result
      changed_when: false

    - name: Parse lsblk JSON output
      ansible.builtin.set_fact:
        disk_info: "{{ lsblk_result.stdout | from_json }}"

    - name: Determine preferred disk for storage
      ansible.builtin.set_fact:
        preferred_disk: >-
          {%- set disks = disk_info.blockdevices | selectattr('type','equalto','disk') | list -%}
          {%- set nvme1n1 = disks | selectattr('name','equalto','nvme1n1') | list -%}
          {%- set sda = disks | selectattr('name','equalto','sda') | list -%}
          {%- if nvme1n1 | length > 0 -%}
            /dev/nvme1n1
          {%- elif sda | length > 0 -%}
            /dev/sda
          {%- else -%}
            {# Sort disks by size and select the largest #}
            {%- set sorted = disks | sort(attribute='size') -%}
            {%- set largest = sorted[-1] -%}
            /dev/{{ largest.name }}
          {%- endif -%}

    - name: Extract site id from hostname
      ansible.builtin.set_fact:
        site_id: "{{ inventory_hostname | regex_replace('\\.eyeq\\.vpn$', '') }}"

    - name: Append/update audit status in CSV file
      delegate_to: localhost
      become: false
      ansible.builtin.lineinfile:
        path: "../eyecue_disk_status.csv"
        regexp: "^{{ site_id }},"
        line: "{{ [ site_id, storage_mounted, (mounted_device | default('n/a')) | trim, preferred_disk | trim ] | join(',') }}"
        create: yes

    - name: Debug audit details
      ansible.builtin.debug:
        msg: >
          Site ID: {{ site_id }},
          Storage Mounted: {{ storage_mounted }},
          Mounted Device: {{ mounted_device }},
          Preferred Disk: {{ preferred_disk }}
