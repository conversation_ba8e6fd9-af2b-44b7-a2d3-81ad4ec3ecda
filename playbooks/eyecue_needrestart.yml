---
# - name: "This playbook runs the needrestart and will schedule a reboot in case of being necessary"
#   gather_facts: false
#   become: true

#   vars:
#     needrestart_dest_path: "/usr/lib/nagios/plugins/check_needrestart"

#   hosts:
#     - "{{ run_hosts }}"

#   tasks:
#     - name: "Copying latest needrestart file to the server"
#       ansible.builtin.copy:
#         src: "../roles/fm.icinga2/files/check_needrestart"
#         dest: "{{ needrestart_dest_path }}"
#         mode: "0775"
#         owner: "root"

#     - name: "Running needrestart check..."
#       ansible.builtin.command:
#         argv:
#           - "{{ needrestart_dest_path }}"
#           - "-n"
#       register: command_output
#       no_log: true
#       ignore_errors: True

#     - ansible.builtin.debug:
#         msg: |
#           "{{ command_output.stdout }}"

#     - name: Checking Cron job...
#       ansible.builtin.slurp:
#         src: "{{ cron_file_path}}"
#       vars:
#         cron_file_path: "/etc/cron.d/needrestart"
#       register: cron_d

#     - ansible.builtin.debug:
#         msg: "{{ cron_d['content'] | b64decode }}"

- name: "This playbook removes needrestart setup"
  gather_facts: false
  become: true

  vars:
    needrestart_dest_path: "/usr/lib/nagios/plugins/check_needrestart"
    cron_file_path: "/etc/cron.d/needrestart"

  hosts:
    - "{{ run_hosts }}"

  tasks:
    - name: "Remove needrestart file from the server"
      ansible.builtin.file:
        path: "{{ needrestart_dest_path }}"
        state: absent

    - name: "Remove cron job file if it exists"
      ansible.builtin.file:
        path: "{{ cron_file_path }}"
        state: absent
