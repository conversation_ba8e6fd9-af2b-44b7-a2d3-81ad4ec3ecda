---
- name: "Setup Redshift Monitoring"
  become: true
  hosts: redshift_monitoring # Instance created by Terraform

  tasks:
    - name: Update Hostname
      block:
        - ansible.builtin.import_role:
            name: hostname
          vars:
            hostname: "redshift-monitoring"
          tags:
            - hostname
            - basics

    - name: "Creating Infra users"
      block:
        - ansible.builtin.import_role:
            name: fm.users
          tags:
            - basics
            - users
            - access
