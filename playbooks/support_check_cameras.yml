---
- name: Ensure check_cameras script exists and run it
  hosts: "{{ run_hosts.split(',') }}"
  become: true
  tasks:
    - name: Check if get_cameras script exists
      ansible.builtin.stat:
        path: /usr/local/bin/get_cameras
      register: get_cameras_script

    - name: Create get_cameras script if it does not exist
      ansible.builtin.copy:
        dest: /usr/local/bin/get_cameras
        content: |
          #!/bin/bash

          # Interfaces are filtered by the word "en"
          INTERFACES=($(ifconfig | awk '/^en/{print $1}' | sed -e "s/://"))
          SUBNETS=()
          CAMERAS=()
          CONTINUE=$1

          if [ ! -z $CONTINUE ]; then
            if [ $CONTINUE == "-y" ]; then
              CONTINUE="y"
            fi
          else
            CONTINUE="N"
          fi

          if [ ! -f /var/cache/icinga2/cameras_ip.txt ]; then
            FILE_EXISTS=1
          else
            FILE_EXISTS=0
          fi

          if [ $FILE_EXISTS -eq 0 -a $CONTINUE == "N" ]; then
            echo "File exists, continue anyway? (y/N)"
            read CONTINUE
            if [ -z $CONTINUE ]; then
              CONTINUE="N"
            fi
          else
            CONTINUE="y"
          fi

          if [ $CONTINUE == "y" ]; then
            for interface in "${INTERFACES[@]}"; do
              SUBNETS+=($(route -n | grep "$interface" | awk {'print $1'} | grep -v 0.0.0.0))
            done

            for subnet in "${SUBNETS[@]}"; do
              # echo "Checking $subnet"
              CAMERAS+=($(nmap "$subnet"/24 -p 554 --open -oG - | awk '/Up$/{print $2}'))
            done

            echo ${CAMERAS[@]} > /var/cache/icinga2/cameras_ip.txt
          else
            echo "Bye!"
          fi
        mode: '0755'
      when: not get_cameras_script.stat.exists

    - name: Run get_cameras script 
      ansible.builtin.command: /usr/local/bin/get_cameras -y
      register: get_cameras_output

    - name: Display script output
      ansible.builtin.debug:
        msg: "{{ get_cameras_output.stdout }}"

    - name: Check if check_cameras script exists
      ansible.builtin.stat:
        path: /usr/lib/nagios/plugins/check_cameras
      register: check_cameras_script

    - name: Create check_cameras script if it does not exist
      ansible.builtin.copy:
        dest: /usr/local/bin/check_cameras
        content: |
          #!/bin/bash

          cameras=$(cat /var/cache/icinga2/cameras_ip.txt)

          if [ -z "$cameras" ]; then
            exit 3
          else
            /usr/lib/nagios/plugins/check_icmp -H $cameras
          fi
        mode: '0755'
      when: not check_cameras_script.stat.exists

    - name: Run check_cameras script
      ansible.builtin.command: /usr/lib/nagios/plugins/check_cameras
      register: script_output
      ignore_errors: true

    - name: Display script output
      ansible.builtin.debug:
        msg: "{{ script_output.stdout }}"
