---
- name: <PERSON><PERSON><PERSON><PERSON>b check
  become: true
  gather_facts: true

  hosts:
    - production
  tasks:
    - command: echo $DISPLAY_NAME
      register: displayname

    - name: CSV - Generate output filename
      set_fact: date="{{lookup('pipe','date +%Y%m%d')}}"
      run_once: true

    - name: CSV - Create file and set the header
      local_action:   
        module: lineinfile
        dest: "/home/<USER>/Downloads/eyecue-servers-serial.cvs"
        line:
          displayname,hostname,serial
        create: yes
        state: present

    - name: CSV - Get IOS devices facts
      set_fact:
        csv_tmp: >
          "{{ displayname.stdout }}","{{ ansible_hostname }}","{{ ansible_product_serial }}"
    - name: CSV - Write information into .csv file
      local_action:   
        module: lineinfile
        insertafter: EOF
        dest: "/home/<USER>/Downloads/eyecue-servers-serial.cvs"
        line: "{{ csv_tmp }}"

    - name: CSV - Blank lines removal
      local_action:   
        module: lineinfile
        path: "/home/<USER>/Downloads/eyecue-servers-serial.cvs"
        state: absent
        regex: '^\s*$'