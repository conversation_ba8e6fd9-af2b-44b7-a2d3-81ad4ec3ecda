---
- name: Check and restart Kubernetes and Docker services
  hosts: "{{ run_hosts.split(',') }}"
  become: true
  gather_facts: true
  vars:
    kube_config_location: "/etc/kubernetes/admin.conf"
  tasks:
    - name: Read Ubuntu version from /etc/issue file
      shell: cat /etc/issue | grep -oP '(?<=Ubuntu\s)\d+'
      register: ubuntu_issue
      changed_when: false

    - name: Extract Ubuntu version
      set_fact:
        ubuntu_version: "{{ ubuntu_issue.stdout }}"

    - name: Print out ubuntu_version
      debug:
        msg: "{{ubuntu_version}}"

    - name: Add Kubernetes APT GPG key
      when: 
        - ansible_os_family == "Debian"
      apt_key:
        url: https://dl.k8s.io/apt/doc/apt-key.gpg
        state: present

    - name: Removing old Kubernetes APT repository 
      when: ansible_os_family == "Debian"
      apt_repository:
        repo: deb http://apt.kubernetes.io/ kubernetes-xenial main
        state: absent
        filename: 'kubernetes'
        update_cache: false

    - name: Add Kubernetes APT repository Xenial
      when: 
        - ansible_os_family == "Debian"
        - ubuntu_version < '22'
      apt_repository:
        repo: deb [trusted=yes] https://fingermark-infra-apt-repo.s3.ap-southeast-2.amazonaws.com kubernetes-xenial main
        state: present
        filename: 'kubernetes'
        update_cache: false

    - name: Add Kubernetes APT repository Jammy
      when: 
        - ansible_os_family == "Debian"
        - ubuntu_version >= '22'      
      apt_repository:
        repo: deb [trusted=yes] https://fingermark-infra-apt-repo-jammy.s3.ap-southeast-2.amazonaws.com kubernetes-jammy main
        state: present
        filename: 'kubernetes'
        update_cache: false
