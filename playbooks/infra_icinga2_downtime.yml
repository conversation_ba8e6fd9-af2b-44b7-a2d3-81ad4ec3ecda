---
- name: Set Icinga2 downtime
  hosts: "localhost"
  connection: local
  become: false
  gather_facts: true

  vars:
    icinga2_api_endpoint: "api.icinga2-master.infra.fingermark.tech"
    icinga2_api_port: "5665"
    icinga2_api_username: "awx"
    icinga2_api_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66373731343834343739383066313233383831356133653962313966346633366135636635396631
          6238313239396330623138663432396636353134393433390a633633376332633232306364643835
          30613335643735396661626666666263353864656234313730653562656663666562643265306339
          6439393464656535390a356231303137393834383932303938643037313766393634633262653865
          34613462376130663462643335343137653633343137356365646565366632646165
    icinga2_api_schedule_downtime_path: "/v1/actions/schedule-downtime"
    icinga2_api_object_type: "Service"
    icinga2_api_affected_service: "cameras"
    icinga2_api_downtime_start: "{{ ansible_date_time.epoch }}"
    icinga2_api_downtime_end: "{{ (ansible_date_time.epoch | int) + (icinga2_api_downtime_duration_sec | int) }}"
    icinga2_api_downtime_duration_sec: "{{ (icinga2_api_downtime_duration_hours | int) * 3600 }}"
    # User input below:
    icinga2_api_affected_host: ""
    icinga2_api_downtime_duration_hours: 1
    icinga2_api_downtime_comment: ""

  tasks:
    - ansible.builtin.debug:
        msg: "Setting downtime for {{ icinga2_api_affected_host }} {{ icinga2_api_affected_service }} for {{ icinga2_api_downtime_duration_sec }} seconds"
    - name: Create an Icinga2 Service Downtime
      ansible.builtin.uri:
        force_basic_auth: true
        url_username: "{{ icinga2_api_username }}"
        url_password: "{{ icinga2_api_password }}"
        url: "https://{{ icinga2_api_endpoint }}:{{ icinga2_api_port }}{{ icinga2_api_schedule_downtime_path }}"
        validate_certs: false
        method: POST
        headers:
          Accept: "application/json"
        body_format: json
        body: |
          {
            "type": "{{ icinga2_api_object_type }}",
            "pretty": true,
            "fixed": false,
            "filter": "\"{{ icinga2_api_affected_host }}\"==host.name && \"{{ icinga2_api_affected_service }}\"==service.name",
            "author": "icingaadmin",
            "comment": "{{ icinga2_api_downtime_comment }}",
            "start_time": "{{ icinga2_api_downtime_start }}",
            "end_time": "{{ icinga2_api_downtime_end }}",
            "duration": "{{ icinga2_api_downtime_duration_sec }}",
            "comment": "{{ icinga2_api_downtime_comment }}"
          }
      when: icinga2_api_object_type == "Service"

    - name: Create an Icinga2 Host Downtime
      ansible.builtin.uri:
        force_basic_auth: true
        url_username: "{{ icinga2_api_username }}"
        url_password: "{{ icinga2_api_password }}"
        url: "https://{{ icinga2_api_endpoint }}:{{ icinga2_api_port }}{{ icinga2_api_schedule_downtime_path }}"
        validate_certs: false
        method: POST
        headers:
          Accept: "application/json"
        body_format: json
        body: |
          {
            "type": "{{ icinga2_api_object_type }}",
            "pretty": true,
            "fixed": false,
            "all_services": true,
            "filter": "\"{{ icinga2_api_affected_host }}\"==host.name",
            "author": "icingaadmin",
            "comment": "{{ icinga2_api_downtime_comment }}",
            "start_time": "{{ icinga2_api_downtime_start }}",
            "end_time": "{{ icinga2_api_downtime_end }}",
            "duration": "{{ icinga2_api_downtime_duration_sec }}",
            "comment": "{{ icinga2_api_downtime_comment }}"
          }
      when: icinga2_api_object_type == "Host"
