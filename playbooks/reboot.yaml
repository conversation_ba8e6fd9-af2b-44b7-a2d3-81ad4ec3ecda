---
- name: Schedule a reboot on the server using cron notation and fetch kernel version. Check https://crontab.guru/
  become: true
  hosts:
    "{{ run_hosts }}"

  tasks:
    - name: Updating cronjob schedule
      ansible.builtin.lineinfile:
        path: /etc/crontab
        line: "0 3 * * 2 root /sbin/shutdown -r +5"
        regexp: "shutdown"

    - name: "Geting server's kernel version"
      ansible.builtin.debug: 
        msg: "{{ ansible_kernel }}"
      register: kernel_info