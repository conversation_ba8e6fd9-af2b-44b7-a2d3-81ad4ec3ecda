---
# This script installs Wire<PERSON> on the target server and creates the configuration on the Wireguard server. You will need SSH access to the Wireguard server
- name: Install Wireguard
  hosts: "{{ run_hosts.split(',') }}"
  become: true

  vars:
    run_hosts: "localhost"
  # Configuration to connect to Wireguard server
    wireguard_server_ip: "wireguard-server.{{customer}}.infra.fingermark.tech"
    wireguard_script_path: "/usr/local/bin"
    wireguard_client_start_automatically: false

  roles:
  - { role: "wireguard-proxy", tags: "vpn" }
