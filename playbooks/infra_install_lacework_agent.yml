---
- name: Install Lacework Agent
  become: true
  gather_facts: true
  hosts: "{{ run_hosts.split(',') }}"

  pre_tasks:
  - name: Enabling WG proxy for USA clients
    ansible.builtin.shell: /usr/local/bin/wg_client_connection.sh up
    when: customer in ('cfa', 'czp')
    tags: 'security'

  roles:
    - role: 'fm.lacework-agent'
      tags: 'security'

  post_tasks:
    - name: Shutdown WG proxy for USA clients
      ansible.builtin.shell: /usr/local/bin/wg_client_connection.sh down
      when: customer in ('cfa', 'czp')
      tags: 'security'