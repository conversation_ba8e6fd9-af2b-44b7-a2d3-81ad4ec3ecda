---

- name: "Clear server disk space"
  hosts: "{{ run_hosts | split(',') | default('localhost')}}"
  gather_facts: true
  become: true 
  vars:
    delete_media_files: false
    microk8s: true

  vars_prompt:
    - name: continue
      prompt: You are about to clear space on the target server. Do you want to continue? (y/N)
      private: false

  tasks:
    - fail:
      when: "continue != 'y'"

      
    - name: Server drive capacity check
      shell: df -h /|awk '/\/dev\//{printf("%d",$5);}'
      register: drive_freespace
      
    - name: Print server drive capacity check
      debug: 
        msg: "{{ drive_freespace.stdout }}"
    - name: Check if the server has less than 60% of disk space
      debug: 
        msg: / does not have the minimum space required to continue (above 60% requested). 
      when: drive_freespace.stdout |float is lt 60

    - name: Prune containers and images
      community.docker.docker_prune:
        containers: true
        images: true
        images_filters:
          dangling: false
      when: drive_freespace.stdout |float is lt 60

