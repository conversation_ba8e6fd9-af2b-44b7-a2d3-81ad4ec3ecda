---
- name: Ensure NVIDIA drivers are installed
  become: true
  hosts: "{{ run_hosts.split(',')}}"

  tasks:
    - name: Check if NVIDIA drivers are installed
      command: nvidia-smi
      register: nvidia_smi_result
      ignore_errors: true

    - name: Output message if NVIDIA drivers were not installed
      debug:
        msg: "NVIDIA drivers are already present."
      when: nvidia_smi_result.rc == 0

    - name: Install NVIDIA drivers
      block:
        - name: Clean up stale NVIDIA driver files
          ansible.builtin.apt:
            name: "nvidia-driver-*"
            state: absent
            purge: yes
          when: nvidia_smi_result.rc != 0

        - name: Install NVIDIA driver
          ansible.builtin.apt:
            name: "{{ item }}"
            state: present
          loop:
            - nvidia-driver-525 

      rescue:
        - ansible.builtin.debug:
            msg: "There's been an ERROR while installing the NVIDIA Drivers. Please Contact Eyecue L3 Support"
      when: nvidia_smi_result.rc != 0


