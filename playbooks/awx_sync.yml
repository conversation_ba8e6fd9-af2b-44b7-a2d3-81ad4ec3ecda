---
- name: This playbook syncs the inventories in awx
  hosts: 127.0.0.1
  vars:
    central_infra_url: "central.infra.fingermark.tech"
    inventory_sources_id: 
      - 11 # AMR 
      - 12 # ANA
      - 14 # CZP
      - 15 # FINGERMARK - DEV
      - 16 # ELJ
      - 17 # GRD
      - 18 # HRD
      - 19 # FINGERMARK - INFRA
      - 20 # KFC
      - 21 # MCD
      - 22 # MCD-NZ
      - 23 # NOD
      - 24 # FINGERMARK - QA
      - 25 # STB
      - 70 # POC


  tasks:

    - name: Update eyecue-server-setup 
      ansible.builtin.uri:
        url: "https://{{ central_infra_url }}/awx/api/v2/projects/8/update/"
        method: POST
        body_format: json
        body: {
              "id": "8"
          }
        headers:
          Authorization: "Bearer {{awx_bearer_token}}"
          Content-Type: application/json
        status_code: 202

    - name: Wait for project to sync
      wait_for:
        delay: 10
        timeout: 0

    - name: Get eyecue-server-setup revision
      ansible.builtin.uri:
        url: "https://{{ central_infra_url }}/awx/api/v2/projects/8/"
        method: GET
        body_format: json
        headers:
          Authorization: "Bearer {{awx_bearer_token}}"
          Content-Type: application/json
        status_code: 200


    - name: Triggering sync for inventory sources
      ansible.builtin.uri:
        url: https://{{ central_infra_url }}/awx/api/v2/inventory_sources/{{ item }}/update/
        method: POST
        body_format: json
        headers:
          Authorization: "Bearer {{awx_bearer_token}}"
          Content-Type: application/json
        status_code: 202
      loop: "{{inventory_sources_id}}"


      tags:
        - sync

    - name: Triggering user playbook to mcd and mnz servers
      ansible.builtin.uri:
        url: https://{{ central_infra_url }}/awx/api/v2/job_templates/67/launch/
        method: POST
        body_format: json
        body: {}
        headers:
          Authorization: "Bearer {{awx_bearer_token}}"
          Content-Type: application/json
        status_code: 201
      tags:
        - users

    - name: Triggering user playbook to eyeq servers
      ansible.builtin.uri:
        url: https://{{ central_infra_url }}/awx/api/v2/job_templates/68/launch/
        method: POST
        body_format: json
        body: {}
        headers:
          Authorization: "Bearer {{awx_bearer_token}}"
          Content-Type: application/json
        status_code: 201
      tags:
        - users



