---
- name: Setup VPN Forwarder (Infra Account ************)
  become: yes

  hosts: 
  - vpn_forwarder

  vars:
    hostname: "vpn-forwarder"
    vpn_user_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          34393637356363323538323230643831626164346538303062386262333864663033306537333366
          3238393530623162346332326436333133333234323865330a383964363232616361643865376566
          37626665393531653663303631623430343434633035323133613037653739346234656337353833
          3333653936333662310a343366623837333035333434663639386661636664366131363563393031
          3666      
    softether_virtualhub: "fm-team"
    softether_username: "awx"
    softether_user_realname: "AWX"

    
  roles:
  - fm.users
  - fm.softether

  tasks:
  - name: Changing hostname
    hostname:
      name: "{{ hostname }}"

  - lineinfile:
      path: /etc/hosts
      line: "*********\t{{ hostname }}\t{{ hostname }}.eyeq.vpn" 
      regexp: "^*********"
  - name: Installing iptables-persistent
    apt:
      name: iptables-persistent

  # https://docs.ansible.com/ansible/latest/collections/ansible/builtin/iptables_module.html
  - name: Enabling forwarding
    ansible.posix.sysctl:
      name: net.ipv4.ip_forward
      value: '1'
      sysctl_set: yes
      state: present
      reload: yes
   
  - ansible.builtin.iptables:
      table: nat
      chain: POSTROUTING
      out_interface: vpn_sfteth
      jump: MASQUERADE
      comment: AWX

  - name: Saving IPTABLES configuration
    shell: "/usr/sbin/iptables-save > /etc/iptables/rules.v4"

  - ansible.builtin.iptables:
      chain: FORWARD
      in_interface: eth0
      jump: ACCEPT
      
  