---
# https://galaxy.ansible.com/gepaplexx/microk8s
- name: This playbook installs and configures a MicroK8s cluster
  hosts: "{{ run_hosts | split(',') | default('localhost')}}"
  become: yes
  vars:
    run_network_configuration_setup: false

  pre_tasks:
    - name: Updating system PATH
      ansible.builtin.lineinfile:
        line: 'export PATH="$PATH:/snap/bin"'
        regexp: /snap/bin
        path: /root/.bashrc
    - name: install kubernetes python package
      shell: pip3 install kubernetes
    - name: Change hostname
      ansible.builtin.hostname:
        name: "{{ hostname | default(ansible_hostname) }}"
      tags:
        - "basics"
    - name: update hosts file
      ansible.builtin.lineinfile:
        path: /etc/hosts
        line: "*********\t{{ hostname | default(ansible_hostname) }}\t{{ hostname | default(ansible_hostname) }}.eyeq.vpn"
        regexp: "^*********"
      tags:
        - "basics"

  roles:
    - role: fm.users
      tags:
        - "basics"

    - role: "fingermark.nvidia-driver"
      vars:
        nvidia_driver_version: "latest"
        nvidia_driver_use_ppa: "ppa:graphics-drivers/ppa"

    # - role: netplan_setup
    #   vars:
    #     server_ip: "{{ server_ip_address }}"
    #     default_gateway: "{{ gateway_ip_address }}"
    #     network_interface_name: "eno2"
    #     netplan_apply_new_config: yes
    #   tags: 
    #     - basics
    #   when: run_network_configuration_setup

    - role: fm.configure_containerd
      tags:
        - "microk8s"

    - role: gepaplexx.microk8s
      tags:
        - "microk8s"
      vars:
        is_master: true
        microk8s_plugins:
          dns: true
          helm3: true
          gpu: true
          # dont add prometheus over here as it needs gpu configuration which is done in the fingermark.kube-monitoring role
          metallb: false

    - role: argocd
      tags: 
        - "eyecue"
      vars:
        delete_eyecue_app: no
        remove_argocd_ns: no
        argocd_bin_update: no
        kubectl: /snap/bin/kubectl
        destination_manifest: /opt/argocd/

    - role: fingermark.kube-monitoring
      tags: 
        - monitoring
      vars:
        thanos_upgrade: no
        thanos_sidecar_install: yes
        node_exporter_standalone_version: no  
        monitoring_install_logging: yes
        delete_logging_ns: no
        dcgm_exporter_namespace: gpu-operator-resources
        microk8s: true
        force_deploy_new_dcgm_exporter: false

    # - role: 'fm.teleport'

    - role: fm.softether
      vars:
        softether_virtualhub: "SERVERS"
        softether_user_realname: "Infra Dev NZL 001"
        vpn_user_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          34393637356363323538323230643831626164346538303062386262333864663033306537333366
          3238393530623162346332326436333133333234323865330a383964363232616361643865376566
          37626665393531653663303631623430343434633035323133613037653739346234656337353833
          3333653936333662310a343366623837333035333434663639386661636664366131363563393031
          3666
      tags: 
        - vpn
      when: run_network_configuration_setup