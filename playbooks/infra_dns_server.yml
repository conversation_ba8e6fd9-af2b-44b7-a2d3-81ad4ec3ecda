---
- name: This playbook installs bind9 and configures it
  hosts: "{{ run_hosts }}"
  become: true

  pre_tasks:
    - name: Starting systemd-resolved service
      ansible.builtin.systemd:
        name: systemd-resolved
        state: started
        enabled: no

  roles:
    - role: fm.users
      vars:
        users:
          - { name: matias, group: admin }
          - { name: shimmy, group: admin }
      tags:
        - users
        - basics
        - access

    - role: fm.softether
      vars:
        vpn_user_password: !vault |
            $ANSIBLE_VAULT;1.1;AES256
            34393637356363323538323230643831626164346538303062386262333864663033306537333366
            3238393530623162346332326436333133333234323865330a383964363232616361643865376566
            37626665393531653663303631623430343434633035323133613037653739346234656337353833
            3333653936333662310a343366623837333035333434663639386661636664366131363563393031
            3666
        softether_virtualhub: "SERVERS"
        softether_client_username: "dns"
        softether_user_realname: "DDNS Server"
        softether_client_private_ip: **************
      tags:
        - vpn
        - softether

    - role: fingermark.dns-server
      vars:
        forwarders: "************; *************; **************;"

      tags:
        - bind
        - dns