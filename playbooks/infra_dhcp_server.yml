---
- name: This playbook installs isc dhcp server and configures it
  hosts: prod_eyecue_dhcp
  become: yes
  
  roles:
  - name: fingermark.dhcp-server
    tags:
      - dhcp

  - name: fm.users
    tags:
      - users
      - access

  - name: fm.softether
    vars: 
      vpn_user_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          34393637356363323538323230643831626164346538303062386262333864663033306537333366
          3238393530623162346332326436333133333234323865330a383964363232616361643865376566
          37626665393531653663303631623430343434633035323133613037653739346234656337353833
          3333653936333662310a343366623837333035333434663639386661636664366131363563393031
          3666
      softether_virtualhub: "SERVERS"
      softether_client_username: "dhcp"
      softether_user_realname: "DHCP Server"
      softether_client_private_ip: *************
    tags:
      - vpn
      - softether