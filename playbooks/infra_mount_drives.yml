---
- name: Mount drives and restore production data
  hosts: "{{ run_hosts.split(',') }}"

  become: yes
  vars:
    storage_mount_path: /media/fingermark/storage
    backup_path: /tmp/storage_backup

  pre_tasks:
    - name: Check if storage mount point is currently mounted
      ansible.builtin.command: findmnt -n "{{ storage_mount_path }}"
      register: mount_info
      changed_when: false
      failed_when: false

    - name: Set mount flag
      ansible.builtin.set_fact:
        storage_mounted: "{{ mount_info.rc == 0 }}"

  tasks:
    - name: Skip processing if storage mount point is already mounted
      ansible.builtin.debug:
        msg: "Storage already mounted, skipping backup and mount tasks."
      when: storage_mounted

    - name: Ensure backup directory exists on target
      ansible.builtin.file:
        path: "{{ backup_path }}"
        state: directory
        mode: '0755'
      when: not storage_mounted

    - name: Backup production files from storage mount path
      ansible.builtin.command: >
        rsync -a "{{ storage_mount_path }}/" "{{ backup_path }}/"
      when: not storage_mounted

    - name: Unmount storage drive if currently mounted
      ansible.builtin.mount:
        path: "{{ storage_mount_path }}"
        state: unmounted
      when: not storage_mounted

    - name: Mount the drive using the fm.mount_drive role
      ansible.builtin.include_role:
        name: fm.mount_drive
      when: not storage_mounted

    - name: Restore production files to storage mount path
      ansible.builtin.command: >
        rsync -a "{{ backup_path }}/" "{{ storage_mount_path }}/"
      when: not storage_mounted

    - name: Remove temporary backup directory
      ansible.builtin.file:
        path: "{{ backup_path }}"
        state: absent
      when: not storage_mounted

  post_tasks:
    - name: Verify storage mount after changes
      ansible.builtin.command: findmnt -n "{{ storage_mount_path }}"
      register: final_mount_info
      changed_when: false
      failed_when: false
      when: not storage_mounted

    - name: Debug final mount info
      ansible.builtin.debug:
        msg: "Storage mount info: {{ final_mount_info.stdout }}"
      when: not storage_mounted
