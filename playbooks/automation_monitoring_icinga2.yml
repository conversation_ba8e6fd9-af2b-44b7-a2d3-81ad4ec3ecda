---
- name: "This playbook is to automate "
  become: true
  gather_facts: true
  hosts: prod_icinga2_master

  vars:
    customer: "{{ customer }}"

  tasks:

    - name: Check if directory exists
      stat:
        path: "/etc/icinga2/zones.s/{{customer}}"
      register: dir_check

    - name: Exit playbook if directory exists
      fail:
        msg: "Directory /etc/icinga2/zones.s/{{customer}} already exists. Exiting playbook."
      when: dir_check.stat.exists

    - name: Create customer zone.
      ansible.builtin.file:
        path: "/etc/icinga2/zones.d/{{ customer }}"
        state: directory
        mode: '0755'
        recurse: yes
        owner: nagios
        group: nagios
      when: not dir_check.stat.exists



    - name: Add customer configs to  to /etc/icinga2/zones.conf
      blockinfile:
        path: /etc/icinga2/zones.conf
        block: |
          object Endpoint "icinga2-satellite.{{ customer }}.infra.fingermark.tech" {
          }

          object Zone "{{ customer }}" {
                  endpoints = [ "icinga2-satellite.{{ customer }}.infra.fingermark.tech" ]
                  parent = "master"
          }

    - name: Check Icinga configuration
      command: icinga2 daemon -C
      register: icinga_check
      ignore_errors: yes

    - name: Fail if Icinga configuration check fails
      fail:
        msg: "Icinga configuration validation failed!"
      when: icinga_check.rc != 0

    - name: Restart Icinga2 service if config check passes
      systemd:
        name: icinga2
        state: restarted
      when: icinga_check.rc == 0