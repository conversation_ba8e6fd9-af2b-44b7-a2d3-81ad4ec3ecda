---
# OpenVPN Client Installation Playbook
# This playbook installs and configures OpenVPN client on edge servers
# to connect to the enhanced whitelist VPN gateway
#
# Usage:
#   ansible-playbook -i your_hosts.yml playbooks/infra_openvpn_client.yml -e "run_hosts=hostname1,hostname2"
#   ansible-playbook -i your_hosts.yml playbooks/infra_openvpn_client.yml -e "run_hosts=hostname1" -e "openvpn_client_auto_start=true"
#
# Requirements:
#   - client.ovpn file must be placed in roles/fm.openvpn-client/files/client.ovpn
#   - Target servers must have eno2 interface (standard for edge servers)

- name: Install and Configure OpenVPN Client
  hosts: "{{ run_hosts.split(',') }}"
  become: true
  gather_facts: true

  vars:
    # VPN Server Configuration
    openvpn_server_hostname: "eyecue-whitelist-vpn-francium-lb-f943a130c03e187d.elb.ap-southeast-2.amazonaws.com"

    # Client Configuration
    openvpn_client_auto_start: false
    openvpn_client_log_level: 3
    openvpn_client_enable_monitoring: false

    # Network Interface Configuration
    # Edge servers use eno2 as primary interface (standard for edge servers)
    openvpn_client_wired_interface: "eno2"

    # OpenVPN Configuration Content
    # The client.ovpn file is automatically loaded from roles/fm.openvpn-client/files/client.ovpn

  pre_tasks:
    - name: Validate required variables
      assert:
        that:
          - openvpn_server_hostname is defined
          - openvpn_server_hostname | length > 0
        fail_msg: "Required variable openvpn_server_hostname must be defined"

    - name: Display configuration summary
      debug:
        msg:
          - "VPN Server: {{ openvpn_server_hostname }}"
          - "Auto Start: {{ openvpn_client_auto_start }}"
          - "Primary Interface: {{ openvpn_client_wired_interface }}"
          - "Target Hosts: {{ ansible_play_hosts }}"

  roles:
    - role: fm.openvpn-client
      tags: 
        - openvpn
        - vpn
        - network

  post_tasks:
    - name: Display post-installation information
      debug:
        msg:
          - "✓ OpenVPN client installation completed successfully!"
          - ""
          - "To test the VPN connection on the edge server:"
          - "  sudo /usr/local/bin/vpn-manager.sh start   # Start VPN"
          - "  sudo /usr/local/bin/vpn-manager.sh status  # Check status"
          - "  sudo /usr/local/bin/vpn-manager.sh stop    # Stop VPN"
          - ""
          - "Service management:"
          - "  sudo systemctl start openvpn-client     # Start VPN service"
          - "  sudo systemctl stop openvpn-client      # Stop VPN service"
          - "  sudo systemctl enable openvpn-client    # Enable auto-start on boot"
          - ""
          - "Configuration: /etc/openvpn/client/client.ovpn"
          - "Logs: /var/log/openvpn/client.log"

    - name: VPN connection ready
      debug:
        msg:
          - "✓ OpenVPN client is configured with actual certificates and ready to use!"
          - ""
          - "To start the VPN connection:"
          - "  sudo /usr/local/bin/vpn-manager.sh start"
          - ""
          - "To check connection status:"
          - "  sudo /usr/local/bin/vpn-manager.sh status"
