---
- name: Add chart repo
  helm_repository:
    binary_path: "{{ helm_binary }}"
    name: test_helm
    repo_url: "{{ chart_test_repo }}"

- name: Install Chart from repository
  include_tasks: "../tests_chart.yml"
  vars:
    source: repository
    chart_source: "test_helm/{{ chart_test }}"
    chart_source_version: "{{ chart_test_version }}"
    chart_source_version_upgrade: "{{ chart_test_version_upgrade }}"
    helm_namespace: "{{ test_namespace[7] }}"

- name: Remove chart repo
  helm_repository:
    binary_path: "{{ helm_binary }}"
    name: test_helm
    repo_url: "{{ chart_test_repo }}"
    state: absent
