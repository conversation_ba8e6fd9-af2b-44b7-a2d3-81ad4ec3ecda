---
- include_tasks: tasks/create.yml
  vars:
    namespace_to_create: "{{ item.name | default(item) }}"
    namespace_labels: "{{ item.labels | default(omit) }}"
  with_items: "{{ test_namespace }}"
  when: test_namespace | type_debug == "list"

- include_tasks: tasks/create.yml
  vars:
    namespace_to_create: "{{ test_namespace }}"
    namespace_labels: "{{ test_namespace_labels | default(omit) }}"
  when: test_namespace | type_debug == "AnsibleUnicode"
