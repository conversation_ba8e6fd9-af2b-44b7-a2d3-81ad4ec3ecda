---
- block:
    - name: Add a daemonset
      k8s:
        definition:
          apiVersion: apps/v1
          kind: DaemonSet
          metadata:
            name: delete-daemonset
            namespace: "{{ test_namespace }}"
          spec:
            selector:
              matchLabels:
                app: "{{ k8s_pod_name }}"
            template: "{{ k8s_pod_template }}"
        wait: yes
        wait_timeout: 400
      vars:
        k8s_pod_name: delete-ds
        k8s_pod_image: gcr.io/kuar-demo/kuard-amd64:1
      register: ds

    - name: Check that daemonset wait worked
      assert:
        that:
          - ds.result.status.currentNumberScheduled == ds.result.status.desiredNumberScheduled

    - name: Check if pods exist
      k8s_info:
        namespace: "{{ test_namespace }}"
        kind: Pod
        label_selectors:
          - "app={{ k8s_pod_name }}"
      vars:
        k8s_pod_name: delete-ds
      register: pods_create

    - name: Assert that there are pods
      assert:
        that:
          - pods_create.resources

    - name: Remove the daemonset
      k8s:
        kind: DaemonSet
        name: delete-daemonset
        namespace: "{{ test_namespace }}"
        state: absent
        wait: yes

    - name: Show status of pods
      k8s_info:
        namespace: "{{ test_namespace }}"
        kind: Pod
        label_selectors:
          - "app={{ k8s_pod_name }}"
      vars:
        k8s_pod_name: delete-ds

    - name: Wait for background deletion
      pause:
        seconds: 30

    - name: Check if pods still exist
      k8s_info:
        namespace: "{{ test_namespace }}"
        kind: Pod
        label_selectors:
          - "app={{ k8s_pod_name }}"
      vars:
        k8s_pod_name: delete-ds
      register: pods_delete

    - name: Assert that deleting the daemonset deleted the pods
      assert:
        that:
          - not pods_delete.resources

    # test deletion using label selector
    - name: Deploy load balancer
      k8s:
        namespace: "{{ test_namespace }}"
        definition:
          apiVersion: v1
          kind: Service
          metadata:
            labels:
              test: deletion
            name: "deletion-svc-{{ item }}"
          spec:
            ports:
              - port: 5000
                targetPort: 5000
            selector:
              test: deletion
            type: LoadBalancer
      with_items:
        - "01"
        - "02"
        - "03"

    - name: Delete services using label selector
      kubernetes.core.k8s:
        api_version: v1
        namespace: "{{ test_namespace }}"
        kind: Service
        state: absent
        label_selectors:
          - test=deletion

    - name: list services using label selector
      k8s_info:
        kind: Service
        namespace: "{{ test_namespace }}"
        label_selectors:
          - test=deletion
      register: _result

    - name: Validate that all services were deleted
      assert:
        that:
          - _result.resources | length == 0

  always:
    - name: Remove namespace
      k8s:
        kind: Namespace
        name: "{{ test_namespace }}"
        state: absent
