- name: Pass a bogus server through the K8S_AUTH_HOST environment variable and ensure helm fails as expected
  helm:
    binary_path: "{{ helm_binary }}"
    state: absent
    name: does-not-exist
    namespace: "{{ helm_namespace }}"
  environment:
    K8S_AUTH_HOST: somewhere
  vars:
    helm_namespace: "{{ test_namespace[2] }}"
  register: _helm_result
  failed_when: '"http://somewhere/version" not in _helm_result.stderr'
