---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: certificates.certmanager.k8s.io
  annotations:
    "api-approved.kubernetes.io": "https://github.com/kubernetes/kubernetes/pull/78458"
spec:
  group: certmanager.k8s.io
  versions:
    - name: v1alpha1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            certificate:
              type: string
            secretName:
              type: string
            dnsNames:
              type: array
              items:
                type: string
            acme:
              type: object
              properties:
                config:
                  type: array
                  items:
                    type: object
                    properties:
                      ingressClass:
                        type: string
                      domains:
                        type: array
                        items:
                          type: string
            issuerRef:
              type: object
              properties:
                name:
                  type: string
                kind:
                  type: string
  scope: Namespaced
  names:
    kind: Certificate
    plural: certificates
    shortNames:
      - cert
      - certs
