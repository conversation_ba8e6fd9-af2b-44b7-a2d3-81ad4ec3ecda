---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: test0
  labels:
    app: nginx
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.14.2
        ports:
        - containerPort: 80
      - name: hello
        image: busybox
        command: ['sh', '-c', 'echo "Hello, from test0" && sleep 3600']
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: test1
  labels:
    app: nginx
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.14.2
        ports:
        - containerPort: 80
      - name: hello
        image: busybox
        command: ['sh', '-c', 'echo "Hello, from test1" && sleep 3600']
