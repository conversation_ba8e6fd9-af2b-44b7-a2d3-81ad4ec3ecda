---
- block:
    - name: Ensure that append_hash namespace exists
      k8s:
        kind: Namespace
        name: "{{ test_namespace }}"

    - name: Create k8s_resource variable
      set_fact:
        k8s_resource:
          metadata:
            name: config-map-test
            namespace: "{{ test_namespace }}"
          apiVersion: v1
          kind: ConfigMap
          data:
            hello: world

    - name: Create config map
      k8s:
        definition: "{{ k8s_resource }}"
        append_hash: yes
      register: k8s_configmap1

    - name: Check configmap is created with a hash
      assert:
        that:
          - k8s_configmap1 is changed
          - k8s_configmap1.result.metadata.name != 'config-map-test'
          - k8s_configmap1.result.metadata.name[:-10] == 'config-map-test-'

    - name: Recreate same config map
      k8s:
        definition: "{{ k8s_resource }}"
        append_hash: yes
      register: k8s_configmap2

    - name: Check configmaps are different
      assert:
        that:
          - k8s_configmap2 is not changed
          - k8s_configmap1.result.metadata.name == k8s_configmap2.result.metadata.name

    - name: Add key to config map
      k8s:
        definition:
          metadata:
            name: config-map-test
            namespace: "{{ test_namespace }}"
          apiVersion: v1
          kind: ConfigMap
          data:
            hello: world
            another: value
        append_hash: yes
      register: k8s_configmap3

    - name: Check configmaps are different
      assert:
        that:
          - k8s_configmap3 is changed
          - k8s_configmap1.result.metadata.name != k8s_configmap3.result.metadata.name

  always:
    - name: Ensure that namespace is removed
      k8s:
        kind: Namespace
        name: "{{ test_namespace }}"
        state: absent
