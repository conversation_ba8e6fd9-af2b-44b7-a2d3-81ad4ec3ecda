---
- name: test copy of large binary and text files
  block:
  - set_fact:
      test_directory: "/tmp/test_k8scp_large_files"
    no_log: true

  - name: create temporary directory for local files
    ansible.builtin.file:
      path: "{{ test_directory }}"
      state: directory

  - name: create large text file
    k8s_create_file:
      path: "{{ test_directory }}/large_text_file.txt"
      size: 150

  - name: create large binary file
    k8s_create_file:
      path: "{{ test_directory }}/large_bin_file.bin"
      size: 200
      binary: true

  # Copy large text file from/to local filesystem to Pod
  - name: copy large file into remote Pod
    k8s_cp:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /large_text_file.txt
      local_path: "{{ test_directory }}/large_text_file.txt"
      state: to_pod

  - name: Compare files
    kubectl_file_compare:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /large_text_file.txt
      local_path: "{{ test_directory }}/large_text_file.txt"

  - name: copy large file from Pod into local filesystem
    k8s_cp:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /large_text_file.txt
      local_path: "{{ test_directory }}/large_text_file_from_pod.txt"
      state: from_pod

  - name: Compare files
    kubectl_file_compare:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /large_text_file.txt
      local_path: "{{ test_directory }}/large_text_file_from_pod.txt"

  # Copy large binary file from/to local filesystem to Pod
  - name: copy large file into remote Pod
    k8s_cp:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /large_bin_file.bin
      local_path: "{{ test_directory }}/large_bin_file.bin"
      state: to_pod

  - name: Compare executable, local vs remote
    kubectl_file_compare:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /large_bin_file.bin
      local_path: "{{ test_directory }}/large_bin_file.bin"

  - name: copy executable from pod into local filesystem
    k8s_cp:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /large_bin_file.bin
      local_path: "{{ test_directory }}/large_bin_file_from_pod.bin"
      state: from_pod

  - name: Compare executable, local vs remote
    kubectl_file_compare:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /large_bin_file.bin
      local_path: "{{ test_directory }}/large_bin_file_from_pod.bin"

  always:
  - name: Delete temporary directory created for the test
    ansible.builtin.file:
      path: "{{ test_directory }}"
      state: absent
    ignore_errors: true

  - name: Delete file created on Pod
    k8s_exec:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      command: 'rm {{ item }}'
    ignore_errors: true
    with_items:
    - /large_text_file.txt
    - /large_bin_file.bin
