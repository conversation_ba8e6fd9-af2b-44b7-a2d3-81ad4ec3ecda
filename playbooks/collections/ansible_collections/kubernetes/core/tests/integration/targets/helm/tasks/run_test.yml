---
- name: Ensure helm is not installed
  file:
    path: "{{ item }}"
    state: absent
  with_items:
    - "/tmp/helm"

- name: Check failed if helm is not installed
  include_tasks: test_helm_not_installed.yml

- name: "Install {{ helm_version }}"
  include_role:
    name: install_helm

- name: "Ensure we honor the environment variables"
  include_tasks: test_read_envvars.yml

- name: Deploy charts
  include_tasks: "tests_chart/{{ test_chart_type }}.yml"
  loop_control:
    loop_var: test_chart_type
  with_items:
    - from_local_path
    - from_repository
    - from_url

- name: test helm dependency update
  include_tasks: test_up_dep.yml

- name: Test helm uninstall
  include_tasks: test_helm_uninstall.yml

# https://github.com/ansible-collections/community.kubernetes/issues/296
- name: Test Skip CRDS feature in helm chart install
  include_tasks: test_crds.yml

- name: Clean helm install
  file:
    path: "{{ item }}"
    state: absent
  with_items:
    - "/tmp/helm/"
