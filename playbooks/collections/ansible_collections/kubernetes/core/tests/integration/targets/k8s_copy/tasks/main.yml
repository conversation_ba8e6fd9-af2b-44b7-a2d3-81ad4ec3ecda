---
- set_fact:
    copy_namespace: "{{ test_namespace }}"

- block:
    # Ensure namespace and create pod to perform tests on
    - name: Ensure namespace exists
      k8s:
        definition:
          apiVersion: v1
          kind: Namespace
          metadata:
            name: "{{ copy_namespace }}"

    - name: Create Pods
      k8s:
        namespace: '{{ copy_namespace }}'
        wait: yes
        template: pods_definition.j2

    - include_tasks: test_copy_errors.yml
    - include_tasks: test_check_mode.yml
    - include_tasks: test_copy_file.yml
    - include_tasks: test_multi_container_pod.yml
    - include_tasks: test_copy_directory.yml
    - include_tasks: test_copy_large_file.yml
    - include_tasks: test_copy_item_with_space_in_its_name.yml

  always:

    - name: Remove namespace
      k8s:
        kind: Namespace
        name: "{{ copy_namespace }}"
        state: absent
      ignore_errors: true
