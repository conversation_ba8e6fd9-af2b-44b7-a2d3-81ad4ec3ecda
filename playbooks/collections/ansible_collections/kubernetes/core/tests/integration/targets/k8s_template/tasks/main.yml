---
- block:
    - set_fact:
        template_namespace: "{{ test_namespace }}"

    - name: Check if k8s_service does not inherit parameter
      kubernetes.core.k8s_service:
        template: "pod_one.j2"
        state: present
      ignore_errors: yes
      register: r

    - name: Check for expected failures in last tasks
      assert:
        that:
          - r.failed
          - "'is only a supported parameter for' in r.msg"

    - name: Specify both definition and template
      kubernetes.core.k8s:
        state: present
        template: "pod_one.j2"
        definition:
          apiVersion: apps/v1
          kind: Deployment
          metadata:
            name: apply-deploy
            namespace: "{{ template_namespace }}"
          spec:
            replicas: 1
            selector:
              matchLabels:
                app: "{{ k8s_pod_name_one }}"
      vars:
        k8s_pod_name_one: pod
        k8s_pod_namespace: "{{ template_namespace }}"
      register: r
      ignore_errors: yes

    - name: Check if definition and template are mutually exclusive
      assert:
        that:
          - r.failed
          - "'parameters are mutually exclusive' in r.msg"

    - name: Specify both src and template
      kubernetes.core.k8s:
        state: present
        src: "../templates/pod_one.j2"
        template: "pod_one.j2"
      vars:
        k8s_pod_name_one: pod
        k8s_pod_namespace: "{{ template_namespace }}"
      register: r
      ignore_errors: yes

    - name: Check if src and template are mutually exclusive
      assert:
        that:
          - r.failed
          - "'parameters are mutually exclusive' in r.msg"

    - name: Create pod using template (direct specification)
      kubernetes.core.k8s:
        template: "pod_one.j2"
        wait: yes
        wait_timeout: "{{ k8s_wait_timeout | default(omit) }}"
      vars:
        k8s_pod_name_one: pod-1
        k8s_pod_namespace: "{{ template_namespace }}"
      register: r

    - name: Assert that pod creation succeeded using template
      assert:
        that:
          - r is successful

    - name: Create pod using template with wrong parameter
      kubernetes.core.k8s:
        template:
          - default
        wait: yes
        wait_timeout: "{{ k8s_wait_timeout | default(omit) }}"
      vars:
        k8s_pod_name_one: pod-2
        k8s_pod_namespace: "{{ template_namespace }}"
      register: r
      ignore_errors: True

    - name: Assert that pod creation failed using template due to wrong parameter
      assert:
        that:
          - r is failed

    - name: Create pod using template (path parameter)
      kubernetes.core.k8s:
        template:
          path: "pod_one.j2"
        wait: yes
        wait_timeout: "{{ k8s_wait_timeout | default(omit) }}"
      vars:
        k8s_pod_name_one: pod-3
        k8s_pod_namespace: "{{ template_namespace }}"
      register: r

    - name: Assert that pod creation succeeded using template
      assert:
        that:
          - r is successful

    - name: Create pod using template (different variable string)
      kubernetes.core.k8s:
        template:
          path: "pod_two.j2"
          variable_start_string: '[['
          variable_end_string: ']]'
        wait: yes
        wait_timeout: "{{ k8s_wait_timeout | default(omit) }}"
      vars:
        k8s_pod_name_two: pod-4
        k8s_pod_namespace: "[[ template_namespace ]]"
        ansible_python_interpreter: "[[ ansible_playbook_python ]]"
      register: r

    - name: Assert that pod creation succeeded using template
      assert:
        that:
          - r is successful

    - name: Create pods using multi-resource template
      kubernetes.core.k8s:
        template:
          path: "pod_three.j2"
        wait: yes
        wait_timeout: "{{ k8s_wait_timeout | default(omit) }}"
      vars:
        k8s_pod_name_three_one: pod-5
        k8s_pod_name_three_two: pod-6
        k8s_pod_namespace: "{{ template_namespace }}"
      register: r

    - name: Assert that pod creation succeeded using template
      assert:
        that:
          - r is successful

    - name: Create pods using list of template
      kubernetes.core.k8s:
        template:
          - pod_one.j2
          - path: "pod_two.j2"
            variable_start_string: '[['
            variable_end_string: ']]'
          - path: "pod_three.j2"
        wait: yes
        wait_timeout: "{{ k8s_wait_timeout | default(omit) }}"
      vars:
        k8s_pod_name_one: pod-7
        k8s_pod_name_two: pod-8
        k8s_pod_name_three_one: pod-9
        k8s_pod_name_three_two: pod-10
        k8s_pod_namespace: "template-test"
      register: r

    - name: Assert that pod creation succeeded using template
      assert:
        that:
          - r is successful

    # continue_on_error
    - name: define variable for test
      set_fact:
        k8s_pod_name_one: pod-11
        k8s_pod_bad_name: pod-12
        k8s_pod_namespace: "{{ template_namespace }}"
        k8s_pod_bad_namespace: "dummy-namespace-012345"

    - name: delete pod if it exists
      kubernetes.core.k8s:
        template: pod_one.j2
        wait: true
        wait_timeout: "{{ k8s_wait_timeout | default(omit) }}"
        state: absent

    - name: create pod on bad namespace ( continue_on_error set to default(false) )
      kubernetes.core.k8s:
        template:
          - pod_with_bad_namespace.j2
          - pod_one.j2
      register: resource
      ignore_errors: true

    - name: validate that creation failed
      assert:
        that:
          - resource is failed
          - '"Failed to create object" in resource.msg'

    - name: assert pod has not been created
      kubernetes.core.k8s_info:
        kind: "{{ item.kind }}"
        namespace: "{{ item.namespace }}"
        name: "{{ item.name }}"
      with_items:
        - kind: pod
          namespace: "{{ k8s_pod_bad_namespace }}"
          name: "{{ k8s_pod_bad_name }}"
        - kind: pod
          namespace: "{{ k8s_pod_name_one }}"
          name: "{{ k8s_pod_namespace }}"
      register: resource

    - name: check that resources creation failed
      assert:
        that:
          - '{{ resource.results[0].resources | length == 0 }}'
          - '{{ resource.results[1].resources | length == 0 }}'

    - name: create pod without namespace (continue_on_error = true)
      kubernetes.core.k8s:
        template:
          - pod_with_bad_namespace.j2
          - pod_one.j2
        continue_on_error: true
        wait: true
        wait_timeout: "{{ k8s_wait_timeout | default(omit) }}"
      register: resource
      ignore_errors: true

    - name: validate that creation succeeded
      assert:
        that:
          - resource is successful

    - name: validate resource creation succeeded for some and failed for others
      assert:
        that:
          - resource is successful
          - resource.result.results | selectattr('changed') | list | length == 1
          - resource.result.results | selectattr('error', 'defined') | list | length == 1

    # Test resource definition using template with 'omit'
    - name: Deploy configmap using template
      k8s:
        namespace: "{{ template_namespace }}"
        name: test-data
        template: configmap.yml.j2

    - name: Read configmap created
      k8s_info:
        kind: configmap
        namespace: "{{ template_namespace }}"
        name: test-data
      register: _configmap

    - name: Validate that the configmap does not contains annotations
      assert:
        that:
          - '"annotations" not in _configmap.resources.0.metadata'

    - name: Create resource once again
      k8s:
        namespace: "{{ template_namespace }}"
        name: test-data
        template: configmap.yml.j2
      register: _configmap

    - name: assert that nothing changed
      assert:
        that:
          - _configmap is not changed

    - name: Create resource once again (using description)
      k8s:
        namespace: "{{ template_namespace }}"
        name: test-data
        template: configmap.yml.j2
      register: _configmap
      vars:
        k8s_configmap_desc: "This is a simple configmap used to test ansible k8s collection"

    - name: assert that configmap was changed
      assert:
        that:
          - _configmap is changed

    - name: Read configmap created
      k8s_info:
        kind: configmap
        namespace: "{{ template_namespace }}"
        name: test-data
      register: _configmap

    - name: Validate that the configmap does not contains annotations
      assert:
        that:
          - _configmap.resources.0.metadata.annotations.description == "This is a simple configmap used to test ansible k8s collection"

  always:
    - name: Remove namespace (Cleanup)
      kubernetes.core.k8s:
        kind: Namespace
        name: "{{ template_namespace }}"
        state: absent
      ignore_errors: true
