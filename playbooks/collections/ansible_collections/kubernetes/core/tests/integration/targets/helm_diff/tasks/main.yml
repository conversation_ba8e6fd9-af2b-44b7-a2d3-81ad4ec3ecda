---
- name: Test helm diff functionality
  vars:
    test_chart_ref: "/tmp/test-chart"
    redis_chart_version: '17.0.5'

  block:

    - name: Install helm diff
      helm_plugin:
        binary_path: "{{ helm_binary }}"
        state: present
        plugin_path: https://github.com/databus23/helm-diff
        plugin_version: 3.4.0

    - name: Copy test chart
      copy:
        src: "test-chart/"
        dest: "{{ test_chart_ref }}"

    - name: Install local chart
      helm:
        binary_path: "{{ helm_binary }}"
        name: test-chart
        namespace: "{{ helm_namespace }}"
        chart_ref: "{{ test_chart_ref }}"
        create_namespace: yes
      register: install

    - assert:
        that:
          - install is changed

    - name: Modify local chart
      blockinfile:
        create: yes
        path: "{{ test_chart_ref }}/templates/anothermap.yaml"
        block: !unsafe |
          apiVersion: v1
          kind: ConfigMap
          metadata:
            name: test-chart-another-configmap
          data:
            foo: {{ .Values.foo | default "bar" }}

    - name: Test helm diff in check mode
      helm:
        binary_path: "{{ helm_binary }}"
        name: test-chart
        namespace: "{{ helm_namespace }}"
        chart_ref: "{{ test_chart_ref }}"
      check_mode: yes
      diff: yes
      register: diff_result

    - name: Check if helm diff check is correct
      vars:
        foo_bar_value: "+   foo: bar"
      assert:
        that:
          - foo_bar_value in diff_result.diff.prepared

    - name: Upgrade local chart with modifications
      helm:
        binary_path: "{{ helm_binary }}"
        name: test-chart
        namespace: "{{ helm_namespace }}"
        chart_ref: "{{ test_chart_ref }}"
      register: install

    - assert:
        that:
          - install is changed

    - name: No diff in check mode when no change
      helm:
        binary_path: "{{ helm_binary }}"
        name: test-chart
        namespace: "{{ helm_namespace }}"
        chart_ref: "{{ test_chart_ref }}"
      check_mode: yes
      diff: yes
      register: diff_result

    - name: Check if no diff in check mode when no change
      assert:
        that:
          - '"diff" not in diff_result'

    - name: Upgrade modified local chart idempotency check
      helm:
        binary_path: "{{ helm_binary }}"
        name: test-chart
        namespace: "{{ helm_namespace }}"
        chart_ref: "{{ test_chart_ref }}"
      register: install

    - assert:
        that:
          - install is not changed

    - name: Modify values
      blockinfile:
        create: yes
        path: "{{ test_chart_ref }}/values.yml"
        block: |
          ---
          foo: baz

    - name: Upgrade with values file
      helm:
        binary_path: "{{ helm_binary }}"
        name: test-chart
        namespace: "{{ helm_namespace }}"
        chart_ref: "{{ test_chart_ref }}"
        values_files:
          - "{{ test_chart_ref }}/values.yml"
      register: install

    - assert:
        that:
          - install is changed

    - name: Upgrade with values file idempotency check
      helm:
        binary_path: "{{ helm_binary }}"
        name: test-chart
        namespace: "{{ helm_namespace }}"
        chart_ref: "{{ test_chart_ref }}"
        values_files:
          - "{{ test_chart_ref }}/values.yml"
      register: install

    - assert:
        that:
          - install is not changed

    - name: Upgrade with values
      helm:
        binary_path: "{{ helm_binary }}"
        name: test-chart
        namespace: "{{ helm_namespace }}"
        chart_ref: "{{ test_chart_ref }}"
        values:
          foo: gaz
      register: install

    - assert:
        that:
          - install is changed

    - name: Upgrade with values idempotency check
      helm:
        binary_path: "{{ helm_binary }}"
        name: test-chart
        namespace: "{{ helm_namespace }}"
        chart_ref: "{{ test_chart_ref }}"
        values:
          foo: gaz
      register: install

    - assert:
        that:
          - install is not changed

    # Test helm diff with chart_repo_url
    - name: Define Redis chart values
      set_fact:
        redis_chart_values:
          commonLabels:
            phase: testing
            company: RedHat
          image:
            tag: 6.2.6-debian-10-r135
          architecture: standalone

    - name: Install Redis chart
      helm:
        binary_path: "{{ helm_binary }}"
        chart_repo_url: https://charts.bitnami.com/bitnami
        chart_ref: redis
        namespace: "{{ helm_namespace }}"
        name: redis-chart
        chart_version: "{{ redis_chart_version }}"
        release_values: "{{ redis_chart_values }}"

    - name: Upgrade Redis chart
      helm:
        binary_path: "{{ helm_binary }}"
        chart_repo_url: https://charts.bitnami.com/bitnami
        chart_ref: redis
        namespace: "{{ helm_namespace }}"
        name: redis-chart
        chart_version: "{{ redis_chart_version }}"
        release_values: "{{ redis_chart_values }}"
      check_mode: yes
      register: redis_upgrade

    - name: Assert that module raised a warning
      assert:
        that:
          - not redis_upgrade.changed
          - redis_upgrade.warnings is defined
          - redis_upgrade.warnings | length == 1
          - redis_upgrade.warnings[0] == "The default idempotency check can fail to report changes in certain cases. Install helm diff >= 3.4.1 for better results."

    - name: Uninstall helm diff
      helm_plugin:
        binary_path: "{{ helm_binary }}"
        state: absent
        plugin_name: diff
      ignore_errors: yes

    - name: Install helm diff (version=3.4.1)
      helm_plugin:
        binary_path: "{{ helm_binary }}"
        state: present
        plugin_path: https://github.com/databus23/helm-diff
        plugin_version: 3.4.1

    - name: Upgrade Redis chart once again
      helm:
        binary_path: "{{ helm_binary }}"
        chart_repo_url: https://charts.bitnami.com/bitnami
        chart_ref: redis
        namespace: "{{ helm_namespace }}"
        name: redis-chart
        chart_version: "{{ redis_chart_version }}"
        release_values: "{{ redis_chart_values }}"
      check_mode: yes
      register: redis_upgrade_2

    - name: Assert that module raised a warning
      assert:
        that:
          - redis_upgrade_2.changed
          - redis_upgrade_2.warnings is not defined

  always:
    - name: Remove chart directory
      file:
        path: "{{ test_chart_ref }}"
        state: absent
      ignore_errors: yes

    - name: Uninstall helm diff
      helm_plugin:
        binary_path: "{{ helm_binary }}"
        state: absent
        plugin_name: diff
      ignore_errors: yes

    - name: Remove helm namespace
      k8s:
        api_version: v1
        kind: Namespace
        name: "{{ helm_namespace }}"
        state: absent
      ignore_errors: yes
