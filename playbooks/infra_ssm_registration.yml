---
- hosts:
  - test

  become: true
  vars:
    # STS Credentials for CVPROD
    aws_access_key_id: ""
    aws_secret_access_key: ""
    aws_session_token: ""
    aws_region: "ap-southeast-2"

    # Path to ssm-registration executable
    executable_path: ../../infra-ssm-migration-tools/dist/ssm-registration

  tasks:
    - name: Ensure Snap is installed
      apt:
        name: snapd
        state: present
        update_cache: yes
      when: ansible_facts.packages.snapd is not defined

    - name: Install the SSM Agent using Snap
      snap:
        name: amazon-ssm-agent
        state: present
        classic: true
      when: ansible_facts.packages['amazon-ssm-agent'] is not defined

    - name: Upload the packaged Python binary
      copy:
        src: "{{ executable_path }}"
        dest: /usr/local/bin/ssm-registration
        mode: '0755'

    # logs will exist on /opt/ssm/registration.log
    - name: Execute the binary
      command: /usr/local/bin/ssm-registration
      environment:
        AWS_ACCESS_KEY_ID: "{{ aws_access_key_id }}"
        AWS_SECRET_ACCESS_KEY: "{{ aws_secret_access_key }}"
        AWS_SESSION_TOKEN: "{{ aws_session_token }}"
        AWS_REGION: "{{ aws_region }}"
        TIMEZONE: "{{ timezone }}"
        DISPLAY_NAME: "{{ display_name }}"

    - name: Start SSM Agent
      systemd:
        name: snap.amazon-ssm-agent.amazon-ssm-agent.service
        state: started
        enabled: yes

    - name: Delete the script after execution
      file:
        path: /usr/local/bin/ssm-registration
        state: absent
