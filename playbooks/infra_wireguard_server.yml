---
- name: Install a Wireguard server to be used as a traffic proxy
  become: yes
  
  hosts: "wireguard_server"

  roles:
    - name: hostname
      vars:
        hostname: "wireguard-server.{{customer}}.infra.fingermark.tech"
        display_name: "Wireguard Proxy Server"
      tags:
        - hostname

    - name: fm.users
      tags:
        - users
        - access

  post_tasks:
    - name: Downloading Wireguard
      ansible.builtin.get_url:
        url: https://raw.githubusercontent.com/angristan/wireguard-install/master/wireguard-install.sh
        dest: /usr/local/bin/wireguard
        mode: 0775

    - name: Creating config directory for clients
      ansible.builtin.file:
        path: "/usr/share/wireguard/clients.d/"
        state: directory
        mode: 0664

    - name: Copying WGNewClient script
      ansible.builtin.copy:
        src: ../roles/wireguard-proxy/files/WGNewClient.sh
        dest: /usr/local/bin/WGNewClient
        mode: 0755

  # After running this playbook, go to the server and run /usr/local/bin/wireguard to install and configure Wireguard. This will trigger a wizard
