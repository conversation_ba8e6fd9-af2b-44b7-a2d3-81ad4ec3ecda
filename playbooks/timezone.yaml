- name: Validate Server Timezones
  hosts: "{{ run_hosts.split(',') }}"
  gather_facts: false
  tasks:
    - name: Get timezone from IP
      uri:
        url: "https://ipinfo.io/json"
        method: GET
        return_content: yes
      register: response

    - name: Parse fetched timezone
      set_fact:
        fetched_timezone: "{{ response.json.timezone | default('Unknown') }}"
      when: response is defined and response.status == 200

    - name: Retrieve inventory timezone
      set_fact:
        inventory_timezone: "{{ hostvars[inventory_hostname].timezone | default('Not Set') }}"

    - name: Read server's current timezone from /etc/timezone
      command: cat /etc/timezone
      register: timezone_file
      ignore_errors: true

    - name: Parse current server timezone
      set_fact:
        current_timezone: "{{ timezone_file.stdout | default('Unknown') }}"

    - name: Compare and display timezone information
      debug:
        msg: |
          Server: {{ inventory_hostname }}
          - Timezone from IP Address: {{ fetched_timezone }}
          - Timezone from System: {{ current_timezone }}
          - Timezone from Inventory: {{ inventory_timezone }}
          - Comparison Result: 
              - Timezone from IP Address vs Timezone from Inventory: {{ 'MATCH' if fetched_timezone == inventory_timezone else 'MISMATCH' }}
              - Timezone from System vs Timezone from Inventory: {{ 'MATCH' if current_timezone == inventory_timezone else 'MISMATCH' }}
              - Timezone from IP Address vs Timezone from System: {{ 'MATCH' if fetched_timezone == current_timezone else 'MISMATCH' }}
    - name: Set fact for mismatched timezones
      set_fact:
        mismatched_timezones: true
      when: fetched_timezone != inventory_timezone or current_timezone != inventory_timezone or fetched_timezone != current_timezone
    - name: Handle mismatched timezones
      debug:
        msg: >
          MISMATCH detected for {{ inventory_hostname }}.
          Consider investigating the timezone differences.
      when: mismatched_timezones is defined
      
