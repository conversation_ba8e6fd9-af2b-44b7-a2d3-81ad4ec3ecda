---
- name: "This playbook configures the Lenovo P3XX server and installs Eyecue"
  become: true
  gather_facts: true
  hosts: "{{ run_hosts.split(',') }}"

  collections:
    - kubernetes.core
  vars:
    gather_facts: true
    run_hosts: "localhost"
    customer: "{{ 'stb-nzl' if 'stb-nzl' in ansible_hostname else ansible_hostname[3:6] }}"
    site_contact_domain: ""
    enable_infra_logging: false
    enable_logging: false
    cleanup_prom_operator: false
    enable_wireguard_proxy: false
    network_setup: true
    kubeconfig_location: "/etc/kubernetes/admin.conf"

    customer_patterns:
      poc: "poc"
      bkg-nzl: "bkg"
      cfa-usa: "cfa"
      cul-usa: "cul"
      czp-usa: "czp"
      elj-aus: "elj"
      kfc-aus: "kfc"
      mnz-nzl: "mnz"
      mcd-aus: "mcd"
      stb-usa: "stb"
      bkg-usa: "bkg-usa"
      pop-nzl: "pop-nzl"
      stb-nzl: "stb-nzl"
      tim-can: "tim-can"
      zmb-aus: "zmb-aus"
      ptl-usa: "ptl-usa"
      mcd-can: "mcd-can"
      pop-usa: "pop-usa"

  pre_tasks:

    - name: Update Hostname
      ansible.builtin.import_role:
        name: hostname
      vars:
        new_hostname: "{{ hostname }}"
      tags:
        - pretask
        - hostname
        - basics

    - name: Set customer variable based on hostname patterns
      set_fact:
        customer: >-
          {{
            customer_patterns | dict2items | selectattr('key', 'in', ansible_hostname) | map(attribute='value') | first
            | default(ansible_hostname[3:6])
          }}
      tags:
        - always

    - name: Set up Timezone
      ansible.builtin.import_role:
        name: timezone
      tags:
        - pretask
        - basics
        - timezone

    - name: "Installing dependencies"
      ansible.builtin.apt:
        name: python3-pip
      tags:
        - pretask
        - always

    - name: Updating system PATH
      ansible.builtin.lineinfile:
        line: 'export PATH="$PATH:/snap/bin"'
        regexp: /snap/bin
        path: /root/.bashrc
      tags:
        - pretask
        - always

    - name: Install required Python packages
      ansible.builtin.pip:
        name:
          - pyyaml
          - kubernetes==28.1.0
          - boto3
        state: present
        extra_args: "--ignore-installed"
      ignore_errors: true
      tags:
        - pretask
        - always

    - block:
      - name: Configure 2nd hard drive
        ansible.builtin.import_role:
          name: fm.mount_drive
        tags:
          - pretask
          - basics
          - storage
      rescue:
        - name: Handle configuration failure
          ansible.builtin.debug:
            msg: "Failed to configure the 2nd hard drive"

  tasks:
    - name: Create Fingermark users
      ansible.builtin.import_role:
        name: fm.users
      tags:
        - basics
        - users
        - access

    # handle exceptions for SoftEther hub names
    - name: Set softether_virtualhub variable
      set_fact:
        softether_virtualhub: >-
          {% if 'pop-nzl' in ansible_hostname %}
            POP
          {% else %}
            {{ customer | default(ansible_hostname[3:6]) | upper }}
          {% endif %}
      tags:
        - access
        - vpn

    - name: Install Softether
      block:
        - ansible.builtin.import_role:
            name: fm.softether
          vars:
            softether_virtualhub: '{{ softether_virtualhub | trim }}'
            vpn_user_password: !vault |
                  $ANSIBLE_VAULT;1.1;AES256
                  34393637356363323538323230643831626164346538303062386262333864663033306537333366
                  3238393530623162346332326436333133333234323865330a383964363232616361643865376566
                  37626665393531653663303631623430343434633035323133613037653739346234656337353833
                  3333653936333662310a343366623837333035333434663639386661636664366131363563393031
                  3666
          tags:
            - access
            - vpn
      rescue:
        - ansible.builtin.debug:
            msg: "There's been an ERROR installing the Softether VPN"
          tags:
            - access
            - vpn

    - name: Install Teleport
      block:
        - ansible.builtin.import_role:
            name: fm.teleport
          tags:
            - access
            - teleport
      rescue:
        - ansible.builtin.debug:
            msg: "There's been an ERROR while installing Teleport"
          tags:
            - access
            - teleport

    - name: Setup network
      block:
        - ansible.builtin.import_role:
            name: netplan_setup
          vars:
            netplan_server_ip: "***********/24"
            netplan_default_gateway: "***********"
            netplan_apply_new_config: yes
            netplan_config_static: false
          when: network_setup
          tags:
            - network

        - name: Enabling WG proxy for USA clients
          ansible.builtin.shell: /usr/local/bin/wg_client_connection.sh up
          when: customer in ('cfa', 'czp') and enable_wireguard_proxy
          tags:
            - always
            - network
            - wireguard_proxy

      rescue:
        - ansible.builtin.debug:
            msg: "There's been an ERROR while setting up the network"
          tags:
            - network

    - name: Install Nvidia container runtime and toolkit
      block:
        - ansible.builtin.import_role:
            name: nvidia_gpu_operator
          tags:
            - nvidia
            - runtime
            - drivers

      rescue:
        - ansible.builtin.debug:
            msg: "There's been an ERROR while installing nvidia container runtime and toolkit"
          tags:
            - nvidia
            - drivers

    - name: Install Lacework Agent
      block:
        - ansible.builtin.import_role:
            name: fm.lacework-agent
          tags:
            - security

      rescue:
        - ansible.builtin.debug:
            msg: "There's been an ERROR while installing Lacework Agent"

    - name: Disabling Ubuntu GUI
      block:
        - ansible.builtin.import_role:
            name: disable-gui
          tags:
            - basics
            - gui
      rescue:
        - ansible.builtin.debug:
            msg: "There's been an ERROR when disabling the server GUI"
          tags:
            - basics
            - gui

    - name: Install NVIDIA Drivers
      block:
        - ansible.builtin.import_role:
            name: fingermark.nvidia-driver
          vars:
            nvidia_driver_version: "535"
            nvidia_driver_use_ppa: true
          tags:
            - nvidia
            - drivers

      rescue:
        - ansible.builtin.debug:
            msg: "There's been an ERROR while installing NVIDIA drivers"
          tags:
            - nvidia
            - drivers

    - name: Install Kubernetes
      block:
        - name: Installing kubeadm, kubectl and kubelet
          ansible.builtin.import_role:
            name: kubernetes/master
          vars:
            kube_version: "v1.20.0"
            kubeadm_opts: "--apiserver-advertise-address '*************'"
          tags:
            - k8s
            - kubernetes

        - name: Installing Calico
          ansible.builtin.import_role:
            name: calico
          vars:
            calico_manifest: /tmp/calico.yaml
            pod_network_cidr: **********/16
          tags:
            - k8s
            - kubernetes
            - calico
      rescue:
        - ansible.builtin.debug:
            msg: "There's been an ERROR while installing Kubernetes"
          tags:
            - k8s
            - kubernetes
            - calico

    - name: Install Host Monitoring
      block:
        - name: Installing Icinga2
          ansible.builtin.import_role:
            name: fm.icinga2
          vars:
            icinga2_node_setup: true
            icinga_force_setup: false
            icinga2_deploy_cameras_check: true
            icinga2_deploy_serialnumber_check: true
            icinga2_deploy_kubernetes_check: true
            icinga2_deploy_prometheus_check: true
          tags:
            - monitoring
            - icinga2
      rescue:
        - ansible.builtin.debug:
            msg: "There's been an ERROR while installing Icinga2"
          tags:
            - monitoring
            - icinga2

    - name: Install K8S Monitoring
      block:
        - name: Installing Prometheus
          ansible.builtin.import_role:
            name: fingermark.kube-monitoring
          vars:
            kube_version: "v1.20.0"
            thanos_upgrade: false
            thanos_sidecar_install: true
            node_exporter_standalone_version: false
            # LOGGING
            monitoring_install_logging: true
            prometheus_operator_uninstall: "{{ cleanup_prom_operator }}"
          tags:
            - monitoring
            - k8s
            - application
            - prometheus
            - kube-monitoring
      rescue:
        - ansible.builtin.debug:
            msg: "There's been an ERROR while installing the the K8S monitoring"
          tags:
            - monitoring
            - k8s
            - application
            - prometheus
            - kube-monitoring

    - name: Install Eyecue application
      block:
        - name: Set argocd_customer variable
          set_fact:
            argocd_customer: >-
              {% if 'mcd-aus' in ansible_hostname %}
                mcdonalds
              {% elif 'bkg-nzl' in ansible_hostname %}
                bkg-nzl
              {% elif 'cul-usa' in ansible_hostname %}
                cul-usa
              {% elif ansible_hostname is search('tcv|tst|dev') %}
                qa
              {% else %}
                {{ customer | lower }}
              {% endif %}
          tags:
            - eyecue
            - argocd
            - application

        - ansible.builtin.import_role:
            name: argocd-helm
          vars:
            argocd_bitbucket_repo: "*****************:fingermarkltd/eyecue-{{ argocd_customer | regex_replace('\\s+', '') }}-helm"
            # Set the organization based on the hostname. E.g. fm-tst-nzl-0001 -> tst-nzl
            argocd_organization: "{{ ansible_hostname | regex_replace('^[^-]+-([^-]+-[^-]+)-.*$', '\\1') }}"
          tags:
            - eyecue
            - argocd
            - application

      rescue:
        - ansible.builtin.debug:
            msg: "There's been an ERROR while installing the ArgoCD or the Eyecue application"
          tags:
            - eyecue
            - argocd
            - application

    - name: Secure server
      block:
        - ansible.builtin.import_role:
            name: geerlingguy.security
          vars:
            security_fail2ban_enabled: false
          tags:
            - security
        - ansible.builtin.import_role:
            name: fm.ubuntu-hardening
          tags:
            - security
            - osquery
            - needrestart
            - debsecan
            - auditd
            - acct
        - ansible.builtin.import_role:
            name: fm.nftables
          tags:
            - security
      rescue:
        - ansible.builtin.debug:
            msg: There's been an ERROR installing while securing the server
          tags:
            - security

    - name: Updating Kommisjon Code
      block:
        - ansible.builtin.import_role:
            name: "kommisjon"
          tags:
            - "kommisjon"
      rescue:
        - ansible.builtin.debug:
            msg: There's been an ERROR updating the Kommisjon client
          tags:
            - "kommisjon"

    - name: Set maximum number of inotify instances
      block:
        - ansible.builtin.import_role:
            name: "fm.inotify_limits"
          tags:
            - "inotify"
      rescue:
        - ansible.builtin.debug:
            msg: ERROR while configuring Inotify
          tags:
            - "inotify"

    - name: Install Eyecue Network CLI
      block:
        - ansible.builtin.import_role:
            name: fm.network-cli
          tags:
            - eyecue-network-cli
      rescue:
        - ansible.builtin.debug:
            msg: "There's been an ERROR while installing the Eyecue Network CLI"
          tags:
            - eyecue-network-cli

  post_tasks:
    - name: "Disabling GPU drivers (Ubuntu 22.04) on system boot to enable boot screen"
      ansible.builtin.lineinfile:
        line: 'GRUB_CMDLINE_LINUX_DEFAULT="quiet nomodeset"'
        regexp: "GRUB_CMDLINE_LINUX_DEFAULT"
        path: "/etc/default/grub"
      notify: "update-grub"
      when: ansible_distribution_version == "22.04"
      tags:
        - drivers
        - nvidia
        - grub

    - name: Create Kommisjon cron tasks
      ansible.builtin.import_role:
        name: kommisjon.cron
      tags:
        - security

    - name: Checking provisioning result
      block:
        - ansible.builtin.import_role:
            name: fm.check_provisioning
          vars:
            slack_channel: "#server-provisioning"
            slack_token: !vault |
              $ANSIBLE_VAULT;1.1;AES256
              66633766643938303630663637363435376539356264393639623466666638646337336362333030
              6138623236353666626236353439353866636230646535350a616165613132303263333730306265
              34353932663639303731303662626363346439663765303664656331366663616335336237633364
              3235316538353439300a376335343461373939373738376365343137356332393966313734376563
              65333761333831373831396666353134383162376539656137303938356339353136653237653762
              3730343735333435613062396662326333316437653364323837
          tags:
            - notifications

    - name: Shutting down the WG proxy for USA clients
      ansible.builtin.shell: /usr/local/bin/wg_client_connection.sh down
      when: customer in ('cfa', 'czp') and enable_wireguard_proxy
      tags:
        - network
        - wireguard_proxy
        - always

  handlers:
    - name: "update-grub"
      command: "update-grub"
      become: true
